<?php
require("database.php");

if (isset($_SESSION["profile_email"])) {
    header("location: ./index.php");
    exit();
} else {
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        $email = $_POST['email'];
        $password = $_POST['password'];

        $sql = "SELECT * FROM profile WHERE profile_email = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();

            // Check if the profile status is active (0) or banned (1)
            if ($user['profile_status'] == 1) {
                // Account is banned
                $error = "Your account has been banned. Please contact support.";
            } else {
                // Account is active, proceed with password verification
                if (password_verify($password, $user['profile_password'])) {
                    $_SESSION['profile_name'] = $user['profile_name'];
                    $_SESSION['profile_email'] = $user['profile_email'];
                    $_SESSION['profile_id'] = $user['profile_id'];
                    $_SESSION['profile_role_id'] = $user['profile_role_id'];

                    // Get organization information if user has admin_id
                    if (!empty($user['admin_id'])) {
                        $orgQuery = "SELECT oa.organization_id, oa.permissions, o.organization_name
                                     FROM organization_admins oa
                                     INNER JOIN organizations o ON oa.organization_id = o.organization_id
                                     WHERE oa.admin_id = ?";
                        $orgStmt = $conn->prepare($orgQuery);
                        $orgStmt->bind_param("i", $user['admin_id']);
                        $orgStmt->execute();
                        $orgResult = $orgStmt->get_result();

                        if ($orgResult->num_rows > 0) {
                            $orgData = $orgResult->fetch_assoc();
                            $_SESSION['admin_id'] = $user['admin_id'];
                            $_SESSION['organization_id'] = $orgData['organization_id'];
                            $_SESSION['organization_name'] = $orgData['organization_name'];
                            $_SESSION['organization_permissions'] = $orgData['permissions']; // JSON from organization_admins table
                        }
                        $orgStmt->close();
                    }

                    // Redirect based on the user's role
                    if ($user['profile_role_id'] == 'gPHOfKV0sL') {
                        header("Location: admin/index.php");
                    } else {
                        header("Location: index.php");
                    }
                    exit();
                } else {
                    $error = "Invalid Username or Password.";
                }
            }
        } else {
            $error = "Invalid Username or Password.";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sport Excel Login Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css" />
    <link rel="stylesheet" href="css/bootstrap-testimonial-slider.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css" />
    <link rel="icon" type="image/x-icon" href="img/sportexcel.ico">
    <style>
        .fade-in {
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }

        .fade-in.visible {
            opacity: 1;
        }
    </style>
</head>

<div id="loading-screen" class="fixed inset-0 bg-white flex items-center justify-center z-50">
    <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
    </div>
</div>

<body class="bg-gray-50 h-screen">
    <!-- Header -->
    <?php include("header.php");?>

    <!-- Main Content -->
    <main class="container mx-auto flex justify-center items-center h-full">
        <div class="flex bg-white rounded-lg shadow-lg w-full md:w-3/4 lg:w-2/3 fade-in">
            <div class="w-1/2 hidden md:flex justify-center items-center bg-gray-200 rounded-l-lg">
                <img src="img/sign_in_banner.jpg" alt="Image" class="object-cover h-full w-full rounded-l-lg">
            </div>
            <div class="w-full md:w-1/2 p-8 md:p-16">
                <h2 class="text-2xl font-bold mb-4">Sign In</h2>
                <form method="POST" action="">
                    <div class="mb-4">
                        <label class="block text-gray-700" for="email">Email Address</label>
                        <div class="flex items-center border-b border-gray-300 py-2">
                            <i class="fas fa-envelope text-gray-400 mr-3"></i>
                            <input class="appearance-none bg-transparent border-none w-full text-gray-700 leading-tight focus:outline-none" type="email" name="email" placeholder="Email Address" aria-label="Email Address" required>
                        </div>
                    </div>
                    <div class="mb-6">
                        <label class="block text-gray-700" for="password">Password</label>
                        <div class="flex items-center border-b border-gray-300 py-2">
                            <i class="fas fa-lock text-gray-400 mr-3"></i>
                            <input class="appearance-none bg-transparent border-none w-full text-gray-700 leading-tight focus:outline-none" type="password" name="password" id="password" placeholder="Password" aria-label="Password" required>
                        </div>
                    </div>
                    <?php if (isset($error)): ?>
                        <div class="text-red-500 mb-4"><?php echo $error; ?></div>
                    <?php endif; ?>
                    <div class="mb-6">
                        <button class="bg-teal-500 hover:bg-teal-700 text-white font-bold py-2 px-4 rounded w-full" type="submit">
                            Sign in
                        </button>
                    </div>
                    <div class="text-center">
                        <p class="text-gray-600"><a href="forgotpassword.php" class="text-teal-500">Reset Password</a></p>
                    </div>
                    <div class="text-center">
                        <p class="text-gray-600">No Account? <a href="register.php" class="text-teal-500">Create One!</a></p>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <?php include("footer.php");?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/js/all.min.js"></script>
    <script>
        document.getElementById('menu-toggle').addEventListener('click', () => {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });

        // Animation to display the section
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.fade-in');
            const options = {
                threshold: 0.1
            };

            const observer = new IntersectionObserver(function(entries, observer) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, options);

            sections.forEach(section => {
                observer.observe(section);
            });
        });

        // Loading animation to ensure the content is well-prepared
        window.addEventListener('load', function() {
            const loadingScreen = document.getElementById('loading-screen');
            const sections = document.querySelectorAll('.fade-in');
            const options = {
                threshold: 0.1
            };

            const observer = new IntersectionObserver(function(entries, observer) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, options);

            sections.forEach(section => {
                observer.observe(section);
            });

            // Hide loading screen when content is fully loaded
            loadingScreen.style.display = 'none';
        });
    </script>
</body>

</html>

