<?php
// Start session if not already started.
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require("../database.php");

$adminisLoggedIn = isset($_SESSION['profile_email']);
$showButton = true; // Default to showing the button

if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Query to check if $session_profile_id exists in event_role_mgm
    $query = "SELECT COUNT(*) as count FROM event_role_mgm WHERE profile_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $session_profile_id);
    $stmt->execute();
    $stmt->bind_result($result_found);
    $stmt->fetch();
    $stmt->close();

    if ($result_found > 0) {
        $showButton = false; // Hide button if the profile is found in event_role_mgm

        // Get all event_mgm_id for this profile_id
        $eventMgmIds = [];
        $query = "SELECT event_mgm_id FROM event_role_mgm WHERE profile_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $session_profile_id);
        $stmt->execute();
        $stmt->bind_result($event_mgm_id);
        while ($stmt->fetch()) {
            $eventMgmIds[] = $event_mgm_id;
        }
        $stmt->close();

        // Fetch event details linked to these event_mgm_ids
        if (!empty($eventMgmIds)) {
            $placeholders = implode(',', array_fill(0, count($eventMgmIds), '?'));
            $query = "
                SELECT DISTINCT e.event_mgm_id, e.event_name, e.event_start_date, e.event_end_date, e.event_venue
                FROM event_mgm e
                WHERE e.event_mgm_id IN ($placeholders)
            ";
            $stmt = $conn->prepare($query);
            // Dynamically bind parameters
            $types = str_repeat('s', count($eventMgmIds));
            $stmt->bind_param($types, ...$eventMgmIds);
            $stmt->execute();
            $result = $stmt->get_result();
            $stmt->close();
        }
    } elseif ($admin_roleid == 'gPHOfKV0sL') {
        // Master admin - Fetch all events
        $query = "
            SELECT e.event_mgm_id,
                   e.event_name,
                   e.event_start_date,
                   e.event_end_date,
                   e.event_venue
            FROM event_mgm e
            WHERE e.event_mgm_id IS NOT NULL
            GROUP BY e.event_mgm_id
            ORDER BY e.event_start_date ASC
        ";
        $result = $conn->query($query);
    } else {
        // Check if user is an organization admin
        if (isset($_SESSION['admin_id']) && isset($_SESSION['organization_id'])) {
            $user_organization_id = $_SESSION['organization_id'];
            $user_permissions = isset($_SESSION['organization_permissions']) ? json_decode($_SESSION['organization_permissions'], true) : [];

            // Check if user has permission to view events
            if (isset($user_permissions['event']['read']) && $user_permissions['event']['read']) {
                // Organization admin - Fetch only their organization's events
                $query = "
                    SELECT e.event_mgm_id,
                           e.event_name,
                           e.event_start_date,
                           e.event_end_date,
                           e.event_venue
                    FROM event_mgm e
                    WHERE e.organization_id = ?
                    GROUP BY e.event_mgm_id
                    ORDER BY e.event_start_date ASC
                ";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("s", $user_organization_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $stmt->close();
            } else {
                // No permission to view events
                header("Location: ../index.php?error=accessdenied");
                exit();
            }
        } else {
            // Redirect unauthorized users
            header("Location: ../index.php?error=accessdenied");
            exit();
        }
    }
} else {
    // Redirect if the user is not logged in
    header("Location: ../login.php?error=pagenotfound");
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Event List</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="icon" type="image/x-icon" href="img/sportexcel.ico">
  <style>
    :root {
      --primary-color: #2563eb;
      --primary-hover: #1d4ed8;
      --secondary-color: #64748b;
      --light-bg: #f8fafc;
      --card-bg: #ffffff;
      --text-dark: #0f172a;
      --text-light: #64748b;
      --border-color: #e2e8f0;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --danger-color: #ef4444;
    }
    
    body {
      background-color: var(--light-bg);
      font-family: 'Poppins', sans-serif;
      color: var(--text-dark);
      line-height: 1.7;
    }
    
    .main-title {
      font-size: 2.25rem;
      font-weight: 700;
      margin: 40px 0 20px 0;
      text-align: center;
      color: var(--text-dark);
      letter-spacing: -0.025em;
      position: relative;
      padding-bottom: 15px;
    }
    
    .main-title:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
      border-radius: 4px;
    }
    
    .container {
      width: 90%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 15px;
    }
    
    .card-container {
      background: var(--card-bg);
      border-radius: 24px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.05);
      padding: 35px 30px;
      margin-bottom: 40px;
      border: 1px solid var(--border-color);
    }
    
    .search-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
      flex-wrap: wrap;
      gap: 15px;
    }
    
    .search-box {
      flex: 1;
      max-width: 400px;
      position: relative;
    }
    
    .search-box input {
      width: 100%;
      padding: 12px 20px;
      padding-left: 45px;
      border: 1px solid var(--border-color);
      border-radius: 12px;
      font-size: 0.95rem;
      transition: all 0.3s;
      background-color: #fff;
    }
    
    .search-box input:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
      outline: none;
    }
    
    .search-box::before {
      content: '\f002';
      font-family: 'Font Awesome 6 Free';
      font-weight: 900;
      position: absolute;
      left: 18px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--secondary-color);
      font-size: 16px;
    }
    
    .btn-create-event {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
      color: white;
      font-weight: 600;
      padding: 12px 24px;
      border-radius: 12px;
      border: none;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s;
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
    }
    
    .btn-create-event:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(37, 99, 235, 0.3);
      background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
    }
    
    .table-responsive {
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0,0,0,0.03);
      border: 1px solid var(--border-color);
      margin-bottom: 30px;
      width: 100%;
      min-width: 100%;
    }
    
    .custom-table {
        width: 100%;
      margin-bottom: 0;
      border-collapse: collapse;
      table-layout: fixed;
    }
    
    .custom-table th {
      background: #f1f5f9;
      color: var(--text-dark);
      font-weight: 600;
      padding: 16px;
      text-align: left;
      border-bottom: 2px solid var(--border-color);
      white-space: nowrap;
    }
    
    /* Column width specifications */
    .custom-table th:nth-child(1) { width: 5%; } /* No column */
    .custom-table th:nth-child(2) { width: 25%; } /* Event Name */
    .custom-table th:nth-child(3) { width: 15%; } /* Category */
    .custom-table th:nth-child(4) { width: 20%; } /* Event Date */
    .custom-table th:nth-child(5) { width: 15%; } /* Event Venue */
    .custom-table th:nth-child(6) { width: 20%; } /* Action */
    
    .custom-table td {
      padding: 16px;
      vertical-align: middle;
      border-bottom: 1px solid var(--border-color);
      color: var(--text-dark);
      transition: all 0.2s;
      word-wrap: break-word;
      overflow: hidden;
      text-overflow: ellipsis;
      }
    
    .custom-table tr:hover td {
      background-color: #f8fafc;
    }
    
    .custom-table tr:last-child td {
      border-bottom: none;
    }
    
    .event-link {
      font-weight: 600;
      color: var(--primary-color);
      text-decoration: none;
      position: relative;
      transition: all 0.2s;
    }
    
    .event-link:hover {
      color: var(--primary-hover);
    }
    
    .event-link:after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 0;
      height: 2px;
      background: var(--primary-hover);
      transition: width 0.3s;
    }
    
    .event-link:hover:after {
      width: 100%;
    }
    
    .category-link {
      color: var(--secondary-color);
      text-decoration: none;
      transition: all 0.2s;
      display: inline-block;
      font-size: 0.9rem;
    }
    
    .category-link:hover {
      color: var(--primary-color);
      text-decoration: underline;
    }
    
    .btn-view {
      background-color: var(--primary-color);
      color: white;
      padding: 8px 18px;
      border-radius: 10px;
      border: none;
      font-weight: 500;
      font-size: 0.9rem;
      transition: all 0.3s;
      text-decoration: none;
      display: inline-block;
      text-align: center;
    }
    
    .btn-view:hover {
      background-color: var(--primary-hover);
      transform: translateY(-1px);
      color: white;
    }
    
    .btn-config {
      background-color: var(--secondary-color);
      color: white;
      padding: 8px 18px;
      border-radius: 10px;
      border: none;
      font-weight: 500;
      font-size: 0.9rem;
      transition: all 0.3s;
      text-decoration: none;
      display: inline-block;
      text-align: center;
      margin-top: 8px;
    }
    
    .btn-config:hover {
      background-color: #475569;
      transform: translateY(-1px);
      color: white;
    }
    
    .pagination {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.9rem;
      color: var(--text-light);
    }
    
    .pagination select {
      border: 1px solid var(--border-color);
      border-radius: 8px;
      padding: 6px 12px;
    }
    
    .modal-content {
      border: none;
      border-radius: 20px;
      box-shadow: 0 15px 40px rgba(0,0,0,0.1);
    }
    
    .modal-header {
      background: linear-gradient(to right, #f1f5f9, #f8fafc);
      border-bottom: 1px solid var(--border-color);
      border-radius: 20px 20px 0 0;
      padding: 20px 30px;
    }
    
    .modal-title {
      font-weight: 700;
      color: var(--text-dark);
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .modal-body {
      padding: 30px;
    }
    
    .modal-footer {
      border-top: 1px solid var(--border-color);
      padding: 20px 30px;
      border-radius: 0 0 20px 20px;
      background: #f8fafc;
    }
    
    .form-label {
      font-weight: 600;
      color: var(--text-dark);
      margin-bottom: 8px;
    }
    
    .form-control {
      border-radius: 12px;
      border: 1px solid var(--border-color);
      padding: 12px 16px;
      font-size: 0.95rem;
      transition: all 0.3s;
    }
    
    .form-control:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
    }
    
    .btn-secondary {
      background: #e2e8f0;
      color: var(--text-dark);
      border: none;
      font-weight: 500;
      border-radius: 10px;
      padding: 10px 20px;
      transition: all 0.2s;
    }
    
    .btn-secondary:hover {
      background: #cbd5e1;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
      color: white;
      border: none;
      font-weight: 600;
      border-radius: 10px;
      padding: 10px 24px;
      transition: all 0.3s;
    }
    
    .btn-primary:hover {
      background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
      transform: translateY(-1px);
    }
    
    @media (max-width: 992px) {
      .search-container {
        flex-direction: column;
        align-items: flex-start;
      }
      
      .search-box {
        max-width: 100%;
        width: 100%;
      }
      
      .card-container {
        padding: 25px 15px;
      }
      
      .custom-table th, 
      .custom-table td {
        padding: 12px 10px;
      }
    }
    
    @media (max-width: 768px) {
      .main-title {
        font-size: 1.75rem;
      }
      
      .table-responsive {
        padding: 0;
      }
      
      .custom-table th, 
      .custom-table td {
        padding: 10px 8px;
        font-size: 0.9rem;
      }
      
      .btn-view, .btn-config {
        padding: 6px 14px;
        font-size: 0.85rem;
      }
    }
  </style>
</head>

<body>
  <!-- Header -->
  <?php include("admin_header.php"); ?>

  <div class="container">
    <h2 class="main-title">Event List</h2>
    
    <div class="card-container">
      <div class="search-container">
        <div class="search-box">
          <input type="text" id="searchBox" placeholder="Search events...">
  </div>
        
        <?php
        // Check if user can create events
        $canCreateEvent = false;
        if ($admin_roleid == 'gPHOfKV0sL') {
            $canCreateEvent = $showButton;
        } elseif (isset($_SESSION['admin_id']) && isset($_SESSION['organization_permissions'])) {
            $user_permissions = json_decode($_SESSION['organization_permissions'], true);
            $canCreateEvent = $showButton && isset($user_permissions['event']['create']) && $user_permissions['event']['create'];
        }

        if ($canCreateEvent): ?>
          <button class="btn-create-event" onclick="location.href='add_event.php'">
            <i class="fas fa-plus"></i> Create Event
          </button>
        <?php endif; ?>
    </div>
      
      <div class="table-responsive">
        <table class="table custom-table">
        <thead>
          <tr>
              <th width="5%">No</th>
              <th width="25%">Event Name</th>
              <th width="15%">Category</th>
              <th width="20%">Event Date</th>
              <th width="15%">Event Venue</th>
              <th width="20%">Action</th>
          </tr>
        </thead>
        <tbody id="scoreListTable">
          <?php if ($result->num_rows > 0): ?>
            <?php $index = 1; ?>
            <?php while ($row = $result->fetch_assoc()): 
              $event_mgm_id = $row['event_mgm_id'];

              // Fetch event id details.
              $sql2 = "SELECT event_id FROM event_mgm WHERE event_mgm_id = ?";
              $stmt2 = $conn->prepare($sql2);
              $stmt2->bind_param("s", $event_mgm_id);
              $stmt2->execute();
              $result2 = $stmt2->get_result();
              $row2 = $result2->fetch_assoc();
              $event_id = $row2['event_id'];
              $stmt2->close();

              // Fetch venue details.
              $venue_id = $row['event_venue'];
              $sql3 = "SELECT golf_club_name FROM club_info WHERE club_id = ?";
              $stmt3 = $conn->prepare($sql3);
              $stmt3->bind_param("s", $venue_id);
              $stmt3->execute();
              $result3 = $stmt3->get_result();
              $row3 = $result3->fetch_assoc();
              $stmt3->close();

              // Fetch mode for the current event.
              $modeQuery = "SELECT mode FROM mode_of_game WHERE event_mgm_id = ?";
              $modeStmt  = $conn->prepare($modeQuery);
              $modeStmt->bind_param("s", $event_mgm_id);
              $modeStmt->execute();
              $modeResult = $modeStmt->get_result();
              $modeRow = $modeResult->fetch_assoc();
              $mode = $modeRow['mode'] ?? 'GROSS';
              $modeStmt->close();

              // For events in NETT mode, fetch configuration values.
              if (strtolower($mode) === 'nett') {
                $nettQuery = "SELECT max_net, max_score FROM nett_format WHERE event_mgm_id = ?";
                $nettStmt = $conn->prepare($nettQuery);
                $nettStmt->bind_param("s", $event_mgm_id);
                $nettStmt->execute();
                $nettResult = $nettStmt->get_result();
                $nettRow = $nettResult->fetch_assoc();
                $nettStmt->close();

                $max_net   = isset($nettRow['max_net']) ? $nettRow['max_net'] : '';
                $max_score = isset($nettRow['max_score']) ? $nettRow['max_score'] : '';
              }
            ?>
            <tr>
              <td><?php echo $index++; ?></td>
              <td>
                  <a href="admin_event_details.php?event_id=<?php echo $event_id; ?>" class="event-link">
                  <?php echo htmlspecialchars($row['event_name']); ?>
                </a>
              </td>
              <td>
                  <a href="Category_type.php?event_mgm_id=<?php echo $event_mgm_id; ?>" class="category-link">
                    Manage category
                </a>
              </td>
              <td><?php echo htmlspecialchars($row['event_start_date']) . " - " . htmlspecialchars($row['event_end_date']); ?></td>
              <td><?php echo htmlspecialchars($row3['golf_club_name']); ?></td>
              <td>
                  <a href="result.php?event_mgm_id=<?php echo htmlspecialchars($event_mgm_id); ?>" class="btn-view">
                    <i class="fas fa-chart-bar me-1"></i> View Result
                  </a>
                <?php
                  if (strtolower($mode) === 'nett') {
                    // Output a button that triggers the modal with a unique id.
                      echo '<a href="#scoreConfigModal_' . htmlspecialchars($event_mgm_id) . '" data-bs-toggle="modal" class="btn-config"><i class="fas fa-cog me-1"></i> Score Config</a>';
                  }
                ?>
              </td>
            </tr>
            <?php
              // If the event is in NETT mode, output the modal for this event.
              if (strtolower($mode) === 'nett') {
            ?>
            <!-- Score Configuration Modal for event <?php echo htmlspecialchars($event_mgm_id); ?> -->
            <div class="modal fade" id="scoreConfigModal_<?php echo htmlspecialchars($event_mgm_id); ?>" tabindex="-1" aria-labelledby="scoreConfigModalLabel_<?php echo htmlspecialchars($event_mgm_id); ?>" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                <form method="POST" action="score_configuration_process.php">
                  <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="scoreConfigModalLabel_<?php echo htmlspecialchars($event_mgm_id); ?>">
                          <i class="fas fa-cog me-2"></i>Score Configuration
                        </h5>
                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-4">
                        <label for="max_net_<?php echo htmlspecialchars($event_mgm_id); ?>" class="form-label">Maximum Under</label>
                        <input type="number" class="form-control" id="max_net_<?php echo htmlspecialchars($event_mgm_id); ?>" name="max_net" value="<?php echo htmlspecialchars($max_net); ?>">
                      </div>
                      <div class="mb-3">
                        <label for="max_score_<?php echo htmlspecialchars($event_mgm_id); ?>" class="form-label">Maximum Score</label>
                        <input type="number" class="form-control" id="max_score_<?php echo htmlspecialchars($event_mgm_id); ?>" name="max_score" value="<?php echo htmlspecialchars($max_score); ?>">
                      </div>
                      <!-- Include the event_mgm_id as a hidden field -->
                      <input type="hidden" name="event_mgm_id" value="<?php echo htmlspecialchars($event_mgm_id); ?>">
                    </div>
                    <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                          <i class="fas fa-save me-2"></i>Save Configuration
                        </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
            <?php
              } // end if NETT mode for modal
            ?>
            <?php endwhile; ?>
          <?php else: ?>
            <tr>
                <td colspan="6" class="text-center py-4">No events found.</td>
            </tr>
          <?php endif; ?>
        </tbody>
      </table>
    </div>
    
    <div class="pagination">
      <span>Showing <?php echo $result->num_rows; ?> entries</span>
        <div class="d-flex align-items-center">
        <label for="entries" class="me-2">Display</label>
        <select id="entries" class="form-select">
          <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
        </select>
        </div>
      </div>
    </div>
  </div>

  <?php include("admin_footer.php"); ?>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // The following scripts handle dropdowns, mobile menu toggle, and search functionality.
    function toggleDropdown(event) {
      event.preventDefault();
      event.stopPropagation();
      const dropdownMenu = event.currentTarget.nextElementSibling;
      const icon = event.currentTarget.querySelector('i.fas');
      document.querySelectorAll('.dropdown-menu').forEach(menu => {
        if (menu !== dropdownMenu) {
          menu.classList.remove('show');
          const otherIcon = menu.previousElementSibling.querySelector('i.fas');
          if (otherIcon) {
            otherIcon.classList.remove('fa-chevron-up');
            otherIcon.classList.add('fa-chevron-down');
          }
        }
      });
      const isShown = dropdownMenu.classList.contains('show');
      dropdownMenu.classList.toggle('show', !isShown);
      icon.classList.toggle('fa-chevron-down', isShown);
      icon.classList.toggle('fa-chevron-up', !isShown);
    }
    
    function closeDropdown(event) {
      document.querySelectorAll('.dropdown-menu').forEach(menu => {
        if (!menu.contains(event.target) && !menu.previousElementSibling.contains(event.target)) {
          menu.classList.remove('show');
          const icon = menu.previousElementSibling.querySelector('i.fas');
          if (icon) {
            icon.classList.remove('fa-chevron-up');
            icon.classList.add('fa-chevron-down');
          }
        }
      });
    }
    
    document.addEventListener('click', closeDropdown);
    
    document.querySelectorAll('.dropdown > a').forEach(toggle => {
      toggle.addEventListener('click', toggleDropdown);
    });
    
    document.getElementById('menu-toggle')?.addEventListener('click', () => {
      document.getElementById('mobile-menu')?.classList.toggle('hidden');
    });
    
    document.getElementById('searchBox').addEventListener('input', function() {
      const filter = this.value.toLowerCase();
      const rows = document.querySelectorAll('#scoreListTable tr');
      rows.forEach(row => {
        const columns = row.querySelectorAll('td');
        let match = false;
        columns.forEach(column => {
          if (column.textContent.toLowerCase().includes(filter)) {
            match = true;
          }
        });
        row.style.display = match ? '' : 'none';
      });
    });
  </script>
</body>

</html>
