<?php
require("../database.php");
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Only master admin can edit organization details
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

$organizationId = $_GET['id'] ?? '';
if (empty($organizationId)) {
    header("Location: organization_list.php");
    exit();
}

// Fetch organization data
$query = "SELECT * FROM organizations WHERE organization_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $organizationId);
$stmt->execute();
$result = $stmt->get_result();
$organization = $result->fetch_assoc();
$stmt->close();

if (!$organization) {
    header("Location: organization_list.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $organizationName = trim($_POST['organization_name']);
    $organizationDescription = trim($_POST['organization_description']);
    $organizationStatus = $_POST['organization_status'];
    $assignmentDate = $_POST['assignment_date'];
    $durationUnit = $_POST['duration_unit'];
    $durationValue = null;

    // Get duration value if not unlimited
    if ($durationUnit != 'unlimited') {
        $durationValue = intval($_POST['duration_value']);
    }

    // Calculate end date based on duration
    $endDate = null;
    if ($durationUnit == 'months' && $durationValue > 0) {
        $endDate = date('Y-m-d', strtotime($assignmentDate . ' + ' . $durationValue . ' months'));
    } elseif ($durationUnit == 'years' && $durationValue > 0) {
        $endDate = date('Y-m-d', strtotime($assignmentDate . ' + ' . $durationValue . ' years'));
    }
    // For 'unlimited', end_date remains null

    if (!empty($organizationName)) {
        // Check if organization name already exists (excluding current organization)
        $checkQuery = "SELECT COUNT(*) FROM organizations WHERE organization_name = ? AND organization_id != ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("ss", $organizationName, $organizationId);
        $stmt->execute();
        $stmt->bind_result($count);
        $stmt->fetch();
        $stmt->close();

        if ($count > 0) {
            echo "<script>alert('Organization name already exists!');</script>";
        } else {
            // Update organization details with duration and assignment date
            $updateQuery = "UPDATE organizations SET organization_name = ?, organization_description = ?, assignment_date = ?, end_date = ?, duration_value = ?, duration_unit = ?, organization_status = ? WHERE organization_id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("ssssisis", $organizationName, $organizationDescription, $assignmentDate, $endDate, $durationValue, $durationUnit, $organizationStatus, $organizationId);

            if ($stmt->execute()) {
                $durationText = $durationUnit == 'unlimited' ? 'unlimited' : $durationValue . ' ' . $durationUnit;
                echo "<script>alert('Organization details updated successfully with duration: $durationText'); window.location.href='organization_list.php';</script>";
            } else {
                echo "<script>alert('Failed to update organization details!');</script>";
            }
            $stmt->close();
        }
    } else {
        echo "<script>alert('Organization name is required!');</script>";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Organization Details</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f3f4f6;
            font-family: 'Inter', sans-serif;
        }

        .main-title {
            font-size: 28px;
            font-weight: 800;
            margin: 20px 0;
            text-align: center;
            color: #1f2937;
        }

        .form-container {
            max-width: 90%;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: border-color 0.2s ease;
            font-size: 14px;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        .btn-submit {
            background-color: #3b82f6;
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            border: none;
            width: 100%;
        }

        .btn-submit:hover {
            background-color: #2563eb;
        }

        .btn-back {
            display: block;
            margin-top: 20px;
            text-align: center;
            color: #3b82f6;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .btn-back:hover {
            color: #2563eb;
        }

        .info-section {
            background-color: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-title {
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 5px;
        }

        .info-text {
            color: #1e40af;
            font-size: 14px;
        }

        .date-section {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .date-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 15px;
            border-bottom: 2px solid #3b82f6;
            padding-bottom: 8px;
        }

        .date-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .duration-config {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .duration-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .duration-input-group input[type="number"] {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            width: 80px;
        }

        .duration-input-group select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background-color: white;
            min-width: 120px;
        }

        .duration-examples {
            color: #6b7280;
            font-style: italic;
        }

        .duration-input-group input[type="number"]:disabled {
            background-color: #f3f4f6;
            color: #9ca3af;
        }

        .date-info {
            background-color: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 12px;
            margin-top: 15px;
            color: #1e40af;
        }

        @media (max-width: 768px) {
            .date-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <?php include("admin_header.php"); ?>

    <div class="container">
        <h2 class="main-title">Edit Organization Details</h2>
        
        <div class="info-section">
            <div class="info-title">Organization Details Only</div>
            <div class="info-text">This page is for editing organization name, description, and status only. To manage permissions, use the "Permissions" action button in the organization list.</div>
        </div>
        
        <div class="form-container">
            <form id="editOrganizationDetailsForm" method="POST" action="edit_organization_details.php?id=<?php echo $organizationId; ?>">
                <div class="form-group">
                    <label for="organization_name">Organization Name *</label>
                    <input type="text" id="organization_name" name="organization_name" value="<?php echo htmlspecialchars($organization['organization_name']); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="organization_description">Description</label>
                    <textarea id="organization_description" name="organization_description" rows="4" placeholder="Enter organization description..."><?php echo htmlspecialchars($organization['organization_description']); ?></textarea>
                </div>
                
                <div class="date-section">
                    <div class="date-title">Organization Assignment Period</div>

                    <div class="date-row">
                        <div class="form-group">
                            <label for="duration_unit">Duration Type *</label>
                            <div class="duration-config">
                                <div class="duration-input-group">
                                    <input type="number" id="duration_value" name="duration_value" min="1" max="99" placeholder="Number" style="width: 80px;" value="<?php echo $organization['duration_value']; ?>">
                                    <select id="duration_unit" name="duration_unit" required>
                                        <option value="months" <?php echo ($organization['duration_unit'] == 'months') ? 'selected' : ''; ?>>Months</option>
                                        <option value="years" <?php echo ($organization['duration_unit'] == 'years') ? 'selected' : ''; ?>>Years</option>
                                        <option value="unlimited" <?php echo ($organization['duration_unit'] == 'unlimited') ? 'selected' : ''; ?>>Unlimited</option>
                                    </select>
                                </div>
                                <div class="duration-examples">
                                    <small>Examples: 3 months, 2 years, unlimited</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="assignment_date">Assignment Date *</label>
                            <input type="date" id="assignment_date" name="assignment_date" value="<?php echo $organization['assignment_date'] ? $organization['assignment_date'] : date('Y-m-d'); ?>" required>
                        </div>
                    </div>

                    <div class="date-info">
                        <strong>Note:</strong>
                        <span id="duration-info">Duration information will be calculated automatically.</span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="organization_status">Status</label>
                    <select id="organization_status" name="organization_status" required>
                        <option value="1" <?php echo ($organization['organization_status'] == 1) ? 'selected' : ''; ?>>Active</option>
                        <option value="0" <?php echo ($organization['organization_status'] == 0) ? 'selected' : ''; ?>>Inactive</option>
                    </select>
                </div>
                
                <button type="submit" class="btn-submit">Update Organization Details</button>
            </form>
            <a href="organization_list.php" class="btn-back">Back to Organization List</a>
            <a href="edit_organization.php?id=<?php echo $organizationId; ?>" class="btn-back">Edit Permissions</a>
        </div>
    </div>

    <!-- Footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>

    <script>
        // Update duration info and handle configurable duration
        document.addEventListener('DOMContentLoaded', function() {
            const durationUnitSelect = document.getElementById('duration_unit');
            const durationValueInput = document.getElementById('duration_value');
            const durationInfo = document.getElementById('duration-info');
            const assignmentDateInput = document.getElementById('assignment_date');

            function updateDurationInfo() {
                const durationUnit = durationUnitSelect.value;
                const durationValue = parseInt(durationValueInput.value) || 0;
                const assignmentDate = assignmentDateInput.value;

                // Enable/disable duration value input
                if (durationUnit === 'unlimited') {
                    durationValueInput.disabled = true;
                    durationValueInput.value = '';
                    durationInfo.textContent = 'This organization will have unlimited access.';
                } else {
                    durationValueInput.disabled = false;
                    durationValueInput.required = true;

                    if (durationValue > 0 && assignmentDate) {
                        const endDate = new Date(assignmentDate);

                        if (durationUnit === 'months') {
                            endDate.setMonth(endDate.getMonth() + durationValue);
                        } else if (durationUnit === 'years') {
                            endDate.setFullYear(endDate.getFullYear() + durationValue);
                        }

                        const durationText = durationValue + ' ' + durationUnit;
                        durationInfo.textContent = `This organization will have access for ${durationText} and expire on ${endDate.toLocaleDateString()}.`;
                    } else if (durationValue > 0) {
                        const durationText = durationValue + ' ' + durationUnit;
                        durationInfo.textContent = `This organization will have access for ${durationText} from the assignment date.`;
                    } else {
                        durationInfo.textContent = `Please enter the number of ${durationUnit}.`;
                    }
                }
            }

            // Add event listeners
            durationUnitSelect.addEventListener('change', updateDurationInfo);
            durationValueInput.addEventListener('input', updateDurationInfo);
            assignmentDateInput.addEventListener('change', updateDurationInfo);

            // Form validation
            document.getElementById('editOrganizationDetailsForm').addEventListener('submit', function(e) {
                const durationUnit = durationUnitSelect.value;
                const durationValue = parseInt(durationValueInput.value) || 0;

                if (durationUnit !== 'unlimited' && durationValue <= 0) {
                    e.preventDefault();
                    alert('Please enter a valid duration value (greater than 0).');
                    durationValueInput.focus();
                    return false;
                }
            });

            // Initial update
            updateDurationInfo();
        });
    </script>
</body>
</html>
