<?php
require("../database.php");
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Get user's organization via admin_id
    $orgQuery = "SELECT oa.organization_id FROM profile p
                 LEFT JOIN organization_admins oa ON p.admin_id = oa.admin_id
                 WHERE p.profile_id = ?";
    $stmt = $conn->prepare($orgQuery);
    $stmt->bind_param("s", $session_profile_id);
    $stmt->execute();
    $stmt->bind_result($user_organization_id);
    $stmt->fetch();
    $stmt->close();

    // Check access permissions
    $is_master_admin = ($admin_roleid == 'gPHOfKV0sL');
    $is_organization_admin = false;

    if (!$is_master_admin) {
        // Check if user is an organization admin
        $adminCheckQuery = "SELECT COUNT(*) as count FROM organization_admins WHERE profile_id = ? AND status = 1";
        $stmt = $conn->prepare($adminCheckQuery);
        $stmt->bind_param("s", $session_profile_id);
        $stmt->execute();
        $stmt->bind_result($admin_count);
        $stmt->fetch();
        $stmt->close();
        $is_organization_admin = ($admin_count > 0);
    }

    // Redirect if user doesn't have proper permissions
    if (!$is_master_admin && !$is_organization_admin) {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}
// Fetch emails from profile table based on organization
if ($is_master_admin) {
    // Master admin can assign users from any organization
    $emailQuery = "SELECT p.profile_id, p.profile_email, p.profile_name, o.organization_name
                   FROM profile p
                   LEFT JOIN organizations o ON p.organization_id = o.organization_id
                   ORDER BY o.organization_name, p.profile_name";
} else {
    // Organization admin can only assign users from their organization
    $emailQuery = "SELECT p.profile_id, p.profile_email, p.profile_name, o.organization_name
                   FROM profile p
                   LEFT JOIN organizations o ON p.organization_id = o.organization_id
                   WHERE p.organization_id = '$user_organization_id'
                   ORDER BY p.profile_name";
}
$emailResult = mysqli_query($conn, $emailQuery);

// Fetch events based on organization
if ($is_master_admin) {
    // Master admin can see all events
    $eventQuery = "SELECT em.event_mgm_id, em.event_name, o.organization_name
                   FROM event_mgm em
                   LEFT JOIN organizations o ON em.organization_id = o.organization_id
                   ORDER BY o.organization_name, em.event_name";
} else {
    // Organization admin can only see events from their organization
    $eventQuery = "SELECT em.event_mgm_id, em.event_name, o.organization_name
                   FROM event_mgm em
                   LEFT JOIN organizations o ON em.organization_id = o.organization_id
                   WHERE em.organization_id = '$user_organization_id'
                   ORDER BY em.event_name";
}
$eventResult = mysqli_query($conn, $eventQuery);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $profileEmail = $_POST['email'];
    $eventMgmId = $_POST['event'];

    // Validate the email exists in the profile table
    $profileQuery = "SELECT profile_id FROM profile WHERE profile_email = ?";
    $stmt = $conn->prepare($profileQuery);
    $stmt->bind_param("s", $profileEmail);
    $stmt->execute();
    $stmt->bind_result($profileId);
    $stmt->fetch();
    $stmt->close();

    if ($profileId && $eventMgmId) {
        // Check if the combination of event_mgm_id and profile_id already exists
        $checkQuery = "SELECT COUNT(*) FROM event_role_mgm WHERE event_mgm_id = ? AND profile_id = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("ss", $eventMgmId, $profileId);
        $stmt->execute();
        $stmt->bind_result($count);
        $stmt->fetch();
        $stmt->close();

        if ($count > 0) {
            echo "<script>alert('This profile is already assigned to the selected event!');</script>";
        } else {
            // Insert into event_role_mgm
            $insertQuery = "INSERT INTO event_role_mgm (event_mgm_id, profile_id) VALUES (?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("ss", $eventMgmId, $profileId);
            if ($stmt->execute()) {
                echo "<script>alert('Event Role added successfully!'); window.location.href='event_role_list.php';</script>";
            } else {
                echo "<script>alert('Failed to add Event Role!');</script>";
            }
            $stmt->close();
        }
    } else {
        echo "<script>alert('Invalid email or event selected!');</script>";
    }
}
?>



<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Event Role</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f3f4f6;
            font-family: 'Inter', sans-serif;
        }

        .main-title {
            font-size: 28px;
            font-weight: 800;
            margin: 20px 0;
            text-align: center;
            color: #1f2937;
        }

        .form-container {
            max-width: 90%;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: border-color 0.2s ease;
            font-size: 14px;
        }

        .form-group input:focus, .form-group select:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        .btn-submit {
            background-color: #3b82f6;
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            border: none;
            width: 100%;
        }

        .btn-submit:hover {
            background-color: #2563eb;
        }

        .btn-back {
            display: block;
            margin-top: 20px;
            text-align: center;
            color: #3b82f6;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .btn-back:hover {
            color: #2563eb;
        }

        @media (max-width: 768px) {
            .main-title {
                font-size: 24px;
                margin: 15px 0;
            }

            .form-container {
                padding: 15px;
            }

            .form-group label {
                font-size: 12px;
            }

            .form-group input, .form-group select {
                padding: 10px;
                font-size: 12px;
            }

            .btn-submit {
                padding: 10px;
                font-size: 14px;
            }

            .btn-back {
                font-size: 14px;
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <?php include("admin_header.php"); ?>

    <div class="container">
        <h2 class="main-title">Add New Event Role</h2>
        <div class="form-container">
            <form id="addEventRoleForm" method="POST" action="add_event_role.php">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" list="emailList" required>
                    <datalist id="emailList">
                        <?php while ($emailRow = mysqli_fetch_assoc($emailResult)) {
                            $displayText = $emailRow['profile_email'];
                            if ($is_master_admin && !empty($emailRow['organization_name'])) {
                                $displayText .= " (" . $emailRow['organization_name'] . ")";
                            }
                        ?>
                            <option value="<?php echo $emailRow['profile_email']; ?>" data-name="<?php echo $emailRow['profile_name']; ?>">
                                <?php echo $displayText; ?>
                            </option>
                        <?php } ?>
                    </datalist>
                </div>
                <div class="form-group">
                    <label for="event">Event</label>
                    <select id="event" name="event" required>
                        <option value="" disabled selected>Select Event</option>
                        <?php while ($eventRow = mysqli_fetch_assoc($eventResult)) {
                            $displayText = $eventRow['event_name'];
                            if ($is_master_admin && !empty($eventRow['organization_name'])) {
                                $displayText .= " (" . $eventRow['organization_name'] . ")";
                            }
                        ?>
                            <option value="<?php echo $eventRow['event_mgm_id']; ?>">
                                <?php echo $displayText; ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
                <button type="submit" class="btn-submit">Add Event Role</button>
            </form>
            <a href="event_role_list.php" class="btn-back">Back to List</a>
        </div>
    </div>

    <!-- Footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>

<script>
        // Disable manual input for the email field
        const emailField = document.getElementById('email');
        emailField.addEventListener('input', function () {
            const datalist = document.getElementById('emailList');
            const options = Array.from(datalist.options).map(option => option.value);
            if (!options.includes(emailField.value)) {
                emailField.setCustomValidity('Please select an email from the list.');
            } else {
                emailField.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
