<?php
require("../database.php");

// Check if user is logged in and is master admin
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_roleid = $_SESSION['profile_role_id'];
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Permissions to Organization Admins</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>

<h1>Add Permissions Column to Organization Admins Table</h1>

<?php

$success_count = 0;
$error_count = 0;

// Step 1: Add permissions column to organization_admins table
echo "<div class='step'>";
echo "<h3>Step 1: Adding permissions column to organization_admins table</h3>";
$check_column = "SHOW COLUMNS FROM organization_admins LIKE 'permissions'";
$result = mysqli_query($conn, $check_column);

if (mysqli_num_rows($result) == 0) {
    $sql = "ALTER TABLE `organization_admins` ADD COLUMN `permissions` JSON DEFAULT NULL COMMENT 'JSON format permissions for this admin'";
    if (mysqli_query($conn, $sql)) {
        echo "<p class='success'>✓ Added permissions column to organization_admins table</p>";
        $success_count++;
    } else {
        echo "<p class='error'>✗ Error adding permissions column: " . mysqli_error($conn) . "</p>";
        $error_count++;
    }
} else {
    echo "<p class='info'>ℹ permissions column already exists in organization_admins table</p>";
}
echo "</div>";

// Step 2: Set default permissions for existing organization admins
echo "<div class='step'>";
echo "<h3>Step 2: Setting default permissions for existing organization admins</h3>";

// Default permissions structure
$default_permissions = json_encode([
    'event' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
    'golf_info' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
    'configure_course' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
    'category_list' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
    'user_list' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
    'role_list' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true]
]);

$sql = "UPDATE organization_admins SET permissions = ? WHERE permissions IS NULL";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $default_permissions);

if ($stmt->execute()) {
    $affected_rows = $stmt->affected_rows;
    echo "<p class='success'>✓ Set default permissions for {$affected_rows} organization admins</p>";
    $success_count++;
} else {
    echo "<p class='error'>✗ Error setting default permissions: " . mysqli_error($conn) . "</p>";
    $error_count++;
}
$stmt->close();
echo "</div>";

// Step 3: Show current organization admins with permissions
echo "<div class='step'>";
echo "<h3>Step 3: Current Organization Admins with Permissions</h3>";
$query = "SELECT oa.admin_id, oa.organization_id, o.organization_name, p.profile_name, p.profile_email, oa.permissions 
          FROM organization_admins oa 
          INNER JOIN organizations o ON oa.organization_id = o.organization_id 
          INNER JOIN profile p ON oa.profile_id = p.profile_id 
          ORDER BY o.organization_name, p.profile_name";
$result = mysqli_query($conn, $query);

if ($result && mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Admin ID</th><th>Organization</th><th>Admin Name</th><th>Email</th><th>Permissions</th></tr>";
    
    while ($row = mysqli_fetch_assoc($result)) {
        $permissions_display = $row['permissions'] ? 'Set' : 'Not Set';
        echo "<tr>";
        echo "<td>" . $row['admin_id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['organization_name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['profile_name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['profile_email']) . "</td>";
        echo "<td>" . $permissions_display . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No organization admins found</p>";
}
echo "</div>";

// Summary
echo "<div class='step'>";
echo "<h3>Migration Summary</h3>";
echo "<p class='success'>✓ Successful operations: {$success_count}</p>";
if ($error_count > 0) {
    echo "<p class='error'>✗ Failed operations: {$error_count}</p>";
}
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>Update organization_admin_list.php to show permissions</li>";
echo "<li>Create permission management interface for organization admins</li>";
echo "<li>Update login.php to get permissions from organization_admins table</li>";
echo "</ul>";
echo "</div>";

?>

</body>
</html>
