<?php
require("../database.php");
$adminisLoggedIn = isset($_SESSION['profile_email']);
$showButton = true;
if (isset($_SESSION['profile_email'])) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid=$_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    $query = "SELECT COUNT(*) as count FROM event_role_mgm WHERE profile_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $session_profile_id);
    $stmt->execute();
    $stmt->bind_result($result_found);
    $stmt->fetch();
    $stmt->close();

    if ($result_found > 0) {
        $showButton = false; // Hide button if profile_id exists in event_role_mgm
    }

    // Check access permissions
    $is_master_admin = ($admin_roleid == 'gPHOfKV0sL');
    $is_organization_admin = false;
    $user_organization_id = null;
    $user_permissions = null;

    if (!$is_master_admin) {
        // Check if user is an organization admin
        if (isset($_SESSION['admin_id']) && isset($_SESSION['organization_id'])) {
            $is_organization_admin = true;
            $user_organization_id = $_SESSION['organization_id'];
            $user_permissions = isset($_SESSION['organization_permissions']) ? json_decode($_SESSION['organization_permissions'], true) : [];
        } else {
            header("Location: ../index.php?error=accessdenied");
            exit();
        }
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit(); // It's a good practice to exit after a redirect
}
function fetch_single_value($conn, $query, $param) {
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $param);
    $stmt->execute();
    $stmt->bind_result($result);
    $stmt->fetch();
    $stmt->close();
    return $result;
}

$event_id = isset($_GET['event_id']) ? $_GET['event_id'] : '1'; 


// Fetching data using the function
$main_title = fetch_single_value($conn, "SELECT event_name FROM event_mgm WHERE event_id = ?", $event_id);
$event_start_date = fetch_single_value($conn, "SELECT event_start_date FROM event_mgm WHERE event_id = ?", $event_id);
$event_end_date = fetch_single_value($conn, "SELECT event_end_date FROM event_mgm WHERE event_id = ?", $event_id);
$event_num_participant = fetch_single_value($conn, "SELECT event_num_participant FROM event_mgm WHERE event_id = ?", $event_id);
$closing_date = fetch_single_value($conn, "SELECT closing_date FROM event_mgm WHERE event_id = ?", $event_id);
$current_participants = fetch_single_value($conn, "SELECT COUNT(*) FROM registration_form WHERE event_id = ?", $event_id);

$query = "SELECT event_mgm_id FROM event_role_mgm WHERE profile_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $session_profile_id);
$stmt->execute();
$result = $stmt->get_result();

$event_mgm_ids = [];
while ($row = $result->fetch_assoc()) {
    $event_mgm_ids[] = $row['event_mgm_id'];
}
$stmt->close();

// Determine the appropriate query to fetch events based on user role
if ($is_master_admin) {
    // Master admin sees all events
    if (!empty($event_mgm_ids)) {
        // If there are matching event_mgm_ids, fetch those events
        $placeholders = implode(',', array_fill(0, count($event_mgm_ids), '?'));
        $events_query = "
            SELECT event_id, event_name, event_start_date, event_end_date,
                   event_num_participant, event_available_status, closing_date
            FROM event_mgm
            WHERE event_mgm_id IN ($placeholders)
            GROUP BY event_mgm_id
        ";
        $stmt = $conn->prepare($events_query);
        $stmt->bind_param(str_repeat('s', count($event_mgm_ids)), ...$event_mgm_ids);
        $stmt->execute();
        $events_result = $stmt->get_result();
    } else {
        // If no matching records, fetch all events
        $events_query = "
            SELECT event_id, event_name, event_start_date, event_end_date,
                   event_num_participant, event_available_status, closing_date
            FROM event_mgm
            GROUP BY event_mgm_id
        ";
        $events_result = $conn->query($events_query);
    }
} else {
    // Organization admin sees only their organization's events
    if (!empty($event_mgm_ids)) {
        // If there are matching event_mgm_ids, fetch those events for this organization
        $placeholders = implode(',', array_fill(0, count($event_mgm_ids), '?'));
        $events_query = "
            SELECT event_id, event_name, event_start_date, event_end_date,
                   event_num_participant, event_available_status, closing_date
            FROM event_mgm
            WHERE event_mgm_id IN ($placeholders) AND organization_id = ?
            GROUP BY event_mgm_id
        ";
        $stmt = $conn->prepare($events_query);
        $params = array_merge($event_mgm_ids, [$user_organization_id]);
        $types = str_repeat('s', count($event_mgm_ids)) . 's';
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $events_result = $stmt->get_result();
    } else {
        // If no matching records, fetch organization's events
        $events_query = "
            SELECT event_id, event_name, event_start_date, event_end_date,
                   event_num_participant, event_available_status, closing_date
            FROM event_mgm
            WHERE organization_id = ?
            GROUP BY event_mgm_id
        ";
        $stmt = $conn->prepare($events_query);
        $stmt->bind_param("s", $user_organization_id);
        $stmt->execute();
        $events_result = $stmt->get_result();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Events Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet"href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" >
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css" />
    <link rel="icon" type="image/x-icon" href="../img/sportexcel.ico">
    <link rel="stylesheet" href="../css/bootstrap-testimonial-slider.min.css" />
<style>
.zoom:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease-in-out;
}
.carousel-control-prev,
.carousel-control-next {
    width: 5%;
}
.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-color: black;
}
@media (max-width: 768px) {
    .carousel-control-prev,
    .carousel-control-next {
        width: 10%;
        top: 50%;
        transform: translateY(-50%);
    }
    .carousel-control-prev-icon,
    .carousel-control-next-icon {
        background-size: 50%, 50%;
    }
}
.fade-in {
    opacity: 0;
    transition: opacity 1s ease-in-out;
}
.fade-in.visible {
    opacity: 1;
}
.event-card {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 350px;
}
.event-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
}
.event-status {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 5px;
    color: #fff;
    border-radius: 5px;
    font-size: 14px;
}
.event-status.full {
    background-color: red;
}
.event-status.available {
    background-color: green;
}
.event-content {
    padding: 10px;
    flex-grow: 1;
    position: relative;
}
.event-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    display: block;
    color: #333;
    text-decoration: none;
}
.event-details {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}
.event-card .image-container {
    position: relative;
    width: 100%;
    height: 180px;
}
.event-card .image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.event-card .image-container .event-date {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 5px;
    border-radius: 3px;
    font-size: 14px;
    color: #666;
}
.venue-card {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 350px;
}
.venue-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
}
.venue-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}
.venue-location, .venue-rating, .venue-capacity {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}
.cta-section {
    background-color: #f9f9f9;
    padding: 40px;
    margin-top: 40px;
    text-align: center;
}
.cta-section img {
    max-width: 100%;
    height: auto;
    margin-bottom: 20px;
}
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
.pagination a {
    padding: 10px 15px;
    margin: 0 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
    text-decoration: none;
    color: #333;
}
.btn-create-event {
    background-color: #2670c5;
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    margin-bottom: 20px;
}
.dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    z-index: 1;
    min-width: 150px;
    border-radius: 5px;
    overflow: hidden;
}
.dropdown-menu a {
    padding: 10px 20px;
    display: block;
    color: #000;
    text-decoration: none;
    white-space: nowrap;
}
.dropdown-menu a:hover {
    background-color: #f1f1f1;
}
.dropdown a {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.dropdown i.fas {
    margin-left: auto;
}
.show {
    display: block !important;
}
@media (max-width: 768px) {
    .header-section {
        padding: 10px 0;
    }
    .container {
        margin-top: 10px;
    }
    .search-box {
        flex-direction: column;
        align-items: stretch;
    }
    .search-box input {
        margin-bottom: 10px;
    }
    .mobile-menu-nav {
        display: flex;
        flex-direction: column;
        padding: 10px 0;
    }
    .mobile-menu-nav a {
        padding: 10px;
        text-decoration: none;
        color: #333;
    }
    .mobile-menu-nav .dropdown-menu {
        position: relative;
        box-shadow: none;
        background: none;
        border: none;
        min-width: 0;
    }
    .mobile-menu-nav .dropdown-menu a {
        padding-left: 20px;
    }
}
.dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background-color: white;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            z-index: 1;
            min-width: 150px;
            border-radius: 5px;
            overflow: hidden;
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .dropdown-menu a {
            padding: 10px 20px;
            display: block;
            color: #000;
            text-decoration: none;
            white-space: nowrap;
        }
        .dropdown-menu a:hover {
            background-color: #f1f1f1;
        }
        .dropdown:hover .dropdown-menu {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }
        .dropdown {
            position: relative;
        }
        .dropdown .dropdown-menu {
            display: none;
        }
        .dropdown:hover .dropdown-menu {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }
        .dropdown-menu a {
            display: block;
            padding: 10px 20px;
            color: #000;
            text-decoration: none;
        }
        .dropdown-menu a:hover {
            background-color: #f1f1f1;
        }

</style>
</head>
<body class="bg-gray-50">
<div id="loading-screen" class="fixed inset-0 bg-white flex items-center justify-center z-50">
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Header -->
    <?php include("admin_header.php");?>
    
    <div class="container my-4">
        <section class="text-center mt-16 fade-in">
            <h1 class="text-3xl font-bold">Our Events</h1>
        </section>
        
        <!-- Search Section -->
        <section class="mt-6 flex flex-col md:flex-row justify-center space-y-4 md:space-y-0 md:space-x-4 fade-in">
            <select class="border border-gray-300 p-2 rounded w-full md:w-64">
                <option>Select Category</option>
                <option>Golf</option>
                <option disabled>Bowling (unavailable)</option>
                <option disabled>Shooting (unavailable)</option>
                <option disabled>Swimming (unavailable)</option>
            </select>
            <select class="border border-gray-300 p-2 rounded w-full md:w-64">
                <option>Select Location</option>
                <option>Johor</option>
                <option>Melaka</option>
                <option>Kuala Lumpur</option>
                <option>Selangor</option>
                <option>Sabah</option>
                <option>Sarawak</option>
            </select>
            <button class="bg-gray-500 text-white py-2 px-10 rounded">Search</button>
        </section>
        
        <!-- Create Event Button -->
        <section class="mt-4 text-right fade-in">
           <?php
           // Check if user can create events
           $canCreateEvent = false;
           if ($is_master_admin) {
               $canCreateEvent = $showButton;
           } elseif ($is_organization_admin && $user_permissions) {
               $canCreateEvent = $showButton && isset($user_permissions['event']['create']) && $user_permissions['event']['create'];
           }

           if ($canCreateEvent): ?>
            <button class="btn-create-event" onclick="location.href='add_event.php'">Create Event</button>
        <?php endif; ?>
        </section>

        <!-- Browse by Category Section -->
        <section class="mt-16"></section>
        
        <!-- Golf Event Section -->
        <section class="mt-16 fade-in">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold">Golf Event</h2>
                <!-- <a href="#" class="text-gray-500">View All (24)</a> -->
            </div>
            <div class="row gy-4 events-grid">
                <!-- Event Cards -->
                <?php while ($event = $events_result->fetch_assoc()) : 
                    $event_mgm_id = fetch_single_value($conn, "SELECT event_mgm_id FROM event_mgm WHERE event_id = ?", $event['event_id']);
                    $query = "
                    SELECT COUNT(*) AS total_registrations
                    FROM registration_form
                    WHERE event_id IN (
                        SELECT event_id
                        FROM event_mgm
                        WHERE event_mgm_id = ?
                    )
                ";

                // Prepare and execute the statement
                if ($stmt = mysqli_prepare($conn, $query)) {
                    mysqli_stmt_bind_param($stmt, "s", $event_mgm_id); 
                    mysqli_stmt_execute($stmt);
                    mysqli_stmt_bind_result($stmt, $total_registrations);
                    mysqli_stmt_fetch($stmt);
                    mysqli_stmt_close($stmt);
                    
                    // Define $current_participants
                    $current_participants = $total_registrations;
                } else {
                    echo "Error: " . mysqli_error($conn);
                }
                    $event_image1 = fetch_single_value($conn, "SELECT event_image1 FROM event_information WHERE event_mgm_id = ?",  $event_mgm_id);
                    $total_acceptance_player = fetch_single_value($conn, "SELECT SUM(event_num_participant) FROM event_mgm WHERE event_mgm_id = ?", $event_mgm_id);
                    $status_tag = "available";

                    // Check if admin closed the event
                    if ($total_acceptance_player == 0) {
                        $status_tag = "full";
                    } else {
                        // Check if participants have reached the limit
                        if ($current_participants >= $total_acceptance_player) {
                            $status_tag = "full";
                        } else {
                            $status_tag = "available";  // Set another tag if not full
                        }
                    }

                    // Check if the current date is past the closing date
                    if (new DateTime() >= new DateTime($event['closing_date'])) {
                        $status_tag = "full";
                    }
                ?>
                <div class="col-md-3 col-sm-6 zoom">
                <a href="admin_event_details.php?event_id=<?php echo $event['event_id']; ?>">
                    <div class="card event-card">
                    <div class="image-container">
                        <img src="../img/<?php echo htmlspecialchars($event_image1); ?>" class="card-img-top" alt="Event Image">
                        <div class="event-status <?php echo $status_tag; ?>"><?php echo strtoupper($status_tag); ?></div>
                        <div class="event-date"><?php echo htmlspecialchars($event['event_start_date']); ?> - <?php echo htmlspecialchars($event['event_end_date']); ?></div>
                    </div>
                        <div class="card-body event-content">
                            <a href="admin_event_details.php?event_id=<?php echo $event['event_id']; ?>" class="event-title"><?php echo htmlspecialchars($event['event_name']); ?></a>
                            <div class="event-details">Up to <?php echo htmlspecialchars($total_acceptance_player); ?> Players</div>
                            <div class="event-details"><b><?php echo htmlspecialchars($current_participants); ?> Registered</b></div>
                        </div>
                    </div>
                </a>
            </div>
            <?php endwhile; ?>
                

            </div>
            <nav class="mt-6">
                <ul class="pagination justify-content-center">
                    <li class="page-item"><a class="page-link" href="#">«</a></li>
                    <li class="page-item"><a class="page-link" href="#">1</a></li>
                    <!-- <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li> -->
                    <li class="page-item"><a class="page-link" href="#">»</a></li>
                </ul>
            </nav>
        </section>
        
        <!-- Popular Venue Section -->
        <section class="mt-16 fade-in">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold">Popular Venue</h2>
                <!-- <a href="#" class="text-gray-500">View All (15)</a> -->
            </div>
            <div class="row gy-4 venue-grid">
                <!-- Venue Cards -->
                <div class="col-md-3 col-sm-6 zoom">
                    <div class="card venue-card">
                        <img src="https://golf-pass.brightspotcdn.com/dims4/default/c64cfb7/2147483647/strip/true/crop/606x391+417+0/resize/930x600!/format/webp/quality/90/?url=https%3A%2F%2Fgolf-pass-brightspot.s3.amazonaws.com%2F8f%2Fbf%2F0ec9bf8f089936ec8c983893d14c%2F109229.jpg" class="card-img-top" alt="Venue Image">
                        <div class="card-body text-center">
                            <div class="venue-title">A'FAMOSA GC</div>
                            <div class="venue-location">Melaka</div>
                            <div class="venue-rating">★★★★★ 5 (22)</div>
                            <div class="venue-capacity">Up to 500 Guests</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 zoom">
                    <div class="card venue-card">
                        <img src="https://image.deemples.com/courses/penang-golf-resort-1671526090.png" class="card-img-top" alt="Venue Image">
                        <div class="card-body text-center">
                            <div class="venue-title">PENANG GOLF RESORT</div>
                            <div class="venue-location">Pulau Penang</div>
                            <div class="venue-rating">★★★★★ 5 (22)</div>
                            <div class="venue-capacity">Up to 500 Guests</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 zoom">
                    <div class="card venue-card">
                        <img src="https://golf-pass.brightspotcdn.com/dims4/default/70013c9/2147483647/strip/true/crop/315x315+268+0/resize/1200x1200!/format/webp/quality/90/?url=https%3A%2F%2Fgolf-pass-brightspot.s3.amazonaws.com%2Ffd%2F6a%2Fef9ad450d7611abd4b20379941a9%2F113927.jpg" class="card-img-top" alt="Venue Image">
                        <div class="card-body text-center">
                            <div class="venue-title">AYER KEROH CC</div>
                            <div class="venue-location">Melaka</div>
                            <div class="venue-rating">★★★★★ 5 (22)</div>
                            <div class="venue-capacity">Up to 500 Guests</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 zoom">
                    <div class="card venue-card">
                        <img src="https://cdn.golflux.com/wp-content/uploads/2023/04/Sabah-Golf-And-Country-Club.jpg" class="card-img-top" alt="Venue Image">
                        <div class="card-body text-center">
                            <div class="venue-title">SABAH GOLF CLUB</div>
                            <div class="venue-location">Sabah</div>
                            <div class="venue-rating">★★★★★ 5 (22)</div>
                            <div class="venue-capacity">Up to 500 Guests</div>
                        </div>
                    </div>
                </div>
                <!-- Add more venue cards as needed -->
            </div>
            <nav class="mt-6">
                <ul class="pagination justify-content-center">
                    <li class="page-item"><a class="page-link" href="#">«</a></li>
                    <li class="page-item"><a class="page-link" href="#">1</a></li>
                    <!-- <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li> -->
                    <li class="page-item"><a class="page-link" href="#">»</a></li>
                </ul>
            </nav>
        </section>
        
        <!-- CTA Section -->
        <section class="mt-16 bg-gray-100 p-4 md:p-16 flex flex-col md:flex-row items-center fade-in">
            <div class="w-full md:w-1/2">
                <img src="../img/sport_excel.jpg" alt="Yayasan Kecemerlangan Sukan Malaysia" class="w-full rounded-lg">
            </div>
            <div class="w-full md:w-1/2 text-left mt-4 md:mt-0 md:pl-10">
                <h2 class="text-2xl font-bold">Yayasan Kecemerlangan Sukan Malaysia</h2>
                <p class="text-gray-600 mt-4">NURTURING JUNIOR SPORTING EXCELLENCE</p>
                <button class="bg-gray-500 text-white py-2 px-6 rounded mt-4">View More</button>
            </div>
        </section>
    </div>

    <!-- footer -->
    <?php include("admin_footer.php");?>

    <script type="text/javascript" src="js/mdb.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
         // Function to toggle dropdown visibility
         function toggleDropdown(event) {
            event.preventDefault(); // Prevent the default action
            event.stopPropagation(); // Prevent the event from bubbling up

            const dropdownMenu = event.currentTarget.nextElementSibling;
            const icon = event.currentTarget.querySelector('i.fas');

            // Close all other open dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== dropdownMenu) {
                    menu.classList.remove('show');
                    const otherIcon = menu.previousElementSibling.querySelector('i.fas');
                    if (otherIcon) {
                        otherIcon.classList.remove('fa-chevron-up');
                        otherIcon.classList.add('fa-chevron-down');
                    }
                }
            });

            // Toggle the current dropdown
            const isShown = dropdownMenu.classList.contains('show');
            dropdownMenu.classList.toggle('show', !isShown);
            icon.classList.toggle('fa-chevron-down', isShown);
            icon.classList.toggle('fa-chevron-up', !isShown);
        }

        // Function to close dropdown when clicking outside
        function closeDropdown(event) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (!menu.contains(event.target) && !menu.previousElementSibling.contains(event.target)) {
                    menu.classList.remove('show');
                    const icon = menu.previousElementSibling.querySelector('i.fas');
                    if (icon) {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }
                }
            });
        }

        // Event listener to close dropdown on clicking outside
        document.addEventListener('click', closeDropdown);

        // Event listeners for each dropdown toggle button
        document.querySelectorAll('.dropdown > a').forEach(toggle => {
            toggle.addEventListener('click', toggleDropdown);
        });

        // Mobile menu toggle
        document.getElementById('menu-toggle').addEventListener('click', () => {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });

        // Animation to display the section
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.fade-in');
            const options = {
                threshold: 0.1
            };

            const observer = new IntersectionObserver(function(entries, observer) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, options);

            sections.forEach(section => {
                observer.observe(section);
            });
        });

        // Loading animation to make sure the content is well prepared
        window.addEventListener('load', function() {
            const loadingScreen = document.getElementById('loading-screen');
            const sections = document.querySelectorAll('.fade-in');
            const options = {
                threshold: 0.1
            };

            const observer = new IntersectionObserver(function(entries, observer) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, options);

            sections.forEach(section => {
                observer.observe(section);
            });

            // Hide loading screen when content is fully loaded
            loadingScreen.style.display = 'none';
        });

    </script>
</body>
</html>
