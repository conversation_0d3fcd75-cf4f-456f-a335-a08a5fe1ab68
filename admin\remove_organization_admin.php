<?php
require("../database.php");
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Only master admin can remove organization admins
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['role_id']) && isset($_POST['profile_id'])) {
    $roleId = $_POST['role_id'];
    $profileId = $_POST['profile_id'];

    // Start transaction
    mysqli_begin_transaction($conn);

    try {
        // Remove role_id from profile table
        $updateQuery = "UPDATE profile SET role_id = NULL WHERE profile_id = ?";
        $stmt1 = $conn->prepare($updateQuery);
        $stmt1->bind_param("s", $profileId);
        $stmt1->execute();
        $stmt1->close();

        // Delete the organization admin assignment
        $deleteQuery = "DELETE FROM organization_admins WHERE role_id = ?";
        $stmt2 = $conn->prepare($deleteQuery);
        $stmt2->bind_param("i", $roleId);
        $stmt2->execute();
        $stmt2->close();

        // Commit transaction
        mysqli_commit($conn);
        echo "<script>alert('Organization admin removed successfully!'); window.location.href='organization_admin_list.php';</script>";

    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        echo "<script>alert('Failed to remove organization admin!'); window.location.href='organization_admin_list.php';</script>";
    }
} else {
    header("Location: organization_admin_list.php");
    exit();
}

mysqli_close($conn);
?>
