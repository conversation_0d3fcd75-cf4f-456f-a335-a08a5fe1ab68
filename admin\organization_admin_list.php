<?php
require("../database.php");
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Only master admin can view organization admin assignments
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

// Fetch organization admin assignments from organization_admins table with permissions
$query = "
    SELECT
        oa.role_id,
        oa.organization_id,
        oa.profile_id,
        oa.assigned_date,
        oa.status,
        oa.permissions,
        oa.role_name,
        o.organization_name,
        p.profile_name,
        p.profile_email
    FROM organization_admins oa
    INNER JOIN organizations o ON oa.organization_id = o.organization_id
    INNER JOIN profile p ON oa.profile_id = p.profile_id
    ORDER BY o.organization_name, oa.role_name
";
$result = mysqli_query($conn, $query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organization Admin Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.8.1/font/bootstrap-icons.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f3f4f6;
            font-family: 'Inter', sans-serif;
        }

        .main-title {
            font-size: 36px;
            font-weight: 800;
            margin: 30px 0;
            text-align: center;
            color: #1f2937;
        }

        .header-section {
            background-color: #ffffff;
            padding: 25px 0;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 30px;
            box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
        }

        .table-container {
            margin-top: 20px;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.05);
            overflow-x: auto;
        }

        .table th, .table td {
            padding: 16px;
            text-align: left;
            vertical-align: middle;
            font-size: 16px;
            border: none;
        }

        .table th {
            background-color: #f9fafb;
            font-weight: 700;
            color: #1f2937;
        }

        .table tbody tr {
            border-bottom: 1px solid #e5e7eb;
        }

        .table tbody tr:hover {
            background-color: #f3f4f6;
        }

        .status-active {
            color: #10b981 !important;
            font-weight: bold;
        }

        .status-inactive {
            color: #ef4444 !important;
            font-weight: bold;
        }

        .icon {
            font-size: 20px;
            cursor: pointer;
            margin: 0 5px;
        }

        .icon.delete-icon {
            color: #ef4444;
            font-size: 24px;
        }

        .icon:hover {
            opacity: 0.7;
        }

        .permission-icon {
            background-color: #10b981;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 16px;
        }

        .permission-icon:hover {
            background-color: #059669;
            transform: scale(1.1);
        }

        .permission-summary {
            font-size: 12px;
            color: #6b7280;
            font-family: monospace;
            text-align: center;
        }
    </style>
</head>
<body class="bg-gray-100">

    <!-- Header -->
    <?php include("admin_header.php"); ?>

    <!-- Header Section -->
    <div class="header-section">
        <h2 class="main-title">Organization Admin Management</h2>
    </div>

    <!-- Add New Button -->
    <div class="container mx-auto px-4">
        <div class="d-flex justify-content-between mb-6">
            <div class="search-box me-3" style="flex-grow: 1;">
                <input type="text" id="searchBox" class="form-control" placeholder="Search Organization Admins...">
            </div>
            <a href="assign_organization_admin.php" class="btn btn-primary">Assign Organization Admin</a>
        </div>

        <!-- Organization Admins Table -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>No.</th>
                        <th>Admin Name</th>
                        <th>Email</th>
                        <th>Organization</th>
                        <th>Assigned Date</th>
                        <th>Status</th>
                        <th>Permissions</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="adminTable">
                    <?php
                    $counter = 1;
                    while ($row = mysqli_fetch_assoc($result)) {
                        $statusText = $row['status'] == 1 ? 'Active' : 'Inactive';
                        $statusClass = $row['status'] == 1 ? 'status-active' : 'status-inactive';
                        $assignedDate = date('Y-m-d', strtotime($row['assigned_date']));

                        // Parse permissions
                        $permissions = json_decode($row['permissions'], true);
                        $permissionSummary = '';
                        if ($permissions) {
                            $permissionCount = 0;
                            foreach ($permissions as $category => $perms) {
                                foreach ($perms as $action => $allowed) {
                                    if ($allowed) $permissionCount++;
                                }
                            }
                            $permissionSummary = $permissionCount . " permissions";
                        } else {
                            $permissionSummary = "No permissions";
                        }

                        // Use role_name if available, otherwise fall back to profile_name
                        $displayName = !empty($row['role_name']) ? $row['role_name'] : $row['profile_name'];

                        echo "<tr>
                                <td>{$counter}</td>
                                <td>{$displayName}</td>
                                <td>{$row['profile_email']}</td>
                                <td>{$row['organization_name']}</td>
                                <td>{$assignedDate}</td>
                                <td class='{$statusClass}'>{$statusText}</td>
                                <td>
                                    <div class='permission-summary'>{$permissionSummary}</div>
                                </td>
                                <td>
                                    <a href='edit_organization_admin_permissions.php?role_id={$row['role_id']}' class='permission-icon' title='Manage Permissions'>
                                        🔒
                                    </a>
                                    <form action='remove_organization_admin.php' method='POST' style='display:inline; margin-left: 10px;'>
                                        <input type='hidden' name='role_id' value='{$row['role_id']}'>
                                        <input type='hidden' name='profile_id' value='{$row['profile_id']}'>
                                        <button type='submit' class='icon delete-icon' onclick='return confirm(\"Are you sure you want to remove this organization admin?\");'>
                                            🗑️
                                        </button>
                                    </form>
                                </td>
                            </tr>";
                        $counter++;
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const adminTable = document.getElementById('adminTable');
            const searchBox = document.getElementById('searchBox');

            // Search Organization Admins
            searchBox.addEventListener('input', () => {
                const searchTerm = searchBox.value.toLowerCase();
                const rows = adminTable.querySelectorAll('tr');

                rows.forEach(row => {
                    const rowText = row.textContent.toLowerCase();
                    row.style.display = rowText.includes(searchTerm) ? '' : 'none';
                });
            });
        });
    </script>
</body>
</html>
