<?php
require("../database.php");

// Check if user is logged in and is master admin
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_roleid = $_SESSION['profile_role_id'];
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Remove organization_id Column from Profile Table</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .container { max-width: 800px; margin: 50px auto; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Remove organization_id Column from Profile Table</h1>
        <p class="warning">⚠️ <strong>WARNING:</strong> This will permanently remove the organization_id column from the profile table!</p>
        <p>This script will:</p>
        <ul>
            <li>1. Check if organization_id column exists in profile table</li>
            <li>2. Remove foreign key constraint (if exists)</li>
            <li>3. Drop the organization_id column</li>
            <li>4. Verify the column has been removed</li>
        </ul>
        <hr>

<?php

$success_count = 0;
$error_count = 0;

// Step 1: Check if organization_id column exists
echo "<div class='step'>";
echo "<h3>Step 1: Checking if organization_id column exists</h3>";
$check_column = "SHOW COLUMNS FROM profile LIKE 'organization_id'";
$result = mysqli_query($conn, $check_column);
if (mysqli_num_rows($result) > 0) {
    echo "<p class='info'>ℹ organization_id column found in profile table</p>";
    
    // Step 2: Remove foreign key constraint first
    echo "<h3>Step 2: Removing foreign key constraint</h3>";
    
    // Check if foreign key constraint exists
    $check_fk = "SELECT CONSTRAINT_NAME 
                 FROM information_schema.KEY_COLUMN_USAGE 
                 WHERE TABLE_SCHEMA = DATABASE() 
                 AND TABLE_NAME = 'profile' 
                 AND COLUMN_NAME = 'organization_id' 
                 AND CONSTRAINT_NAME != 'PRIMARY'";
    
    $fk_result = mysqli_query($conn, $check_fk);
    if ($fk_result && mysqli_num_rows($fk_result) > 0) {
        while ($fk_row = mysqli_fetch_assoc($fk_result)) {
            $constraint_name = $fk_row['CONSTRAINT_NAME'];
            $drop_fk_sql = "ALTER TABLE `profile` DROP FOREIGN KEY `{$constraint_name}`";
            
            if (mysqli_query($conn, $drop_fk_sql)) {
                echo "<p class='success'>✓ Removed foreign key constraint: {$constraint_name}</p>";
                $success_count++;
            } else {
                echo "<p class='error'>✗ Error removing foreign key constraint {$constraint_name}: " . mysqli_error($conn) . "</p>";
                $error_count++;
            }
        }
    } else {
        echo "<p class='info'>ℹ No foreign key constraints found for organization_id column</p>";
    }
    
    // Step 3: Drop the organization_id column
    echo "<h3>Step 3: Dropping organization_id column</h3>";
    $drop_column_sql = "ALTER TABLE `profile` DROP COLUMN `organization_id`";
    
    if (mysqli_query($conn, $drop_column_sql)) {
        echo "<p class='success'>✓ Successfully removed organization_id column from profile table</p>";
        $success_count++;
    } else {
        echo "<p class='error'>✗ Error removing organization_id column: " . mysqli_error($conn) . "</p>";
        $error_count++;
    }
    
} else {
    echo "<p class='info'>ℹ organization_id column does not exist in profile table (already removed)</p>";
}
echo "</div>";

// Step 4: Verify column has been removed
echo "<div class='step'>";
echo "<h3>Step 4: Verification</h3>";
$verify_column = "SHOW COLUMNS FROM profile LIKE 'organization_id'";
$verify_result = mysqli_query($conn, $verify_column);
if (mysqli_num_rows($verify_result) == 0) {
    echo "<p class='success'>✓ Confirmed: organization_id column has been successfully removed from profile table</p>";
    $success_count++;
} else {
    echo "<p class='error'>✗ Error: organization_id column still exists in profile table</p>";
    $error_count++;
}
echo "</div>";

// Step 5: Show current profile table structure
echo "<div class='step'>";
echo "<h3>Step 5: Current Profile Table Structure</h3>";
$show_columns = "SHOW COLUMNS FROM profile";
$columns_result = mysqli_query($conn, $show_columns);

if ($columns_result) {
    echo "<table class='table table-bordered'>";
    echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
    echo "<tbody>";
    while ($column = mysqli_fetch_assoc($columns_result)) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
} else {
    echo "<p class='error'>✗ Error showing table structure: " . mysqli_error($conn) . "</p>";
}
echo "</div>";

// Summary
echo "<div class='step'>";
echo "<h3>Operation Summary</h3>";
echo "<p class='success'>✓ Successful operations: {$success_count}</p>";
if ($error_count > 0) {
    echo "<p class='error'>✗ Failed operations: {$error_count}</p>";
} else {
    echo "<p class='success'>✓ All operations completed successfully!</p>";
}

echo "<h4>What was done:</h4>";
echo "<ul>";
echo "<li>✓ Removed foreign key constraints for organization_id</li>";
echo "<li>✓ Dropped organization_id column from profile table</li>";
echo "<li>✓ Verified column removal</li>";
echo "<li>✓ System now uses admin_id approach exclusively</li>";
echo "</ul>";

echo "<h4>Next Steps:</h4>";
echo "<ul>";
echo "<li>1. Test organization assignment functionality</li>";
echo "<li>2. Verify all organization-related features work correctly</li>";
echo "<li>3. Check that admin_id relationships are working properly</li>";
echo "</ul>";
echo "</div>";

mysqli_close($conn);
?>

    </div>
    
    <div class="container mt-4">
        <div class="text-center">
            <a href="organization_admin_list.php" class="btn btn-primary">Go to Organization Admin List</a>
            <a href="user_list.php" class="btn btn-secondary">Go to User List</a>
        </div>
    </div>
</body>
</html>
