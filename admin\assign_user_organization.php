<?php
require("../database.php");
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Only master admin can assign users to organizations
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

// Fetch organizations
$orgQuery = "SELECT organization_id, organization_name FROM organizations WHERE organization_status = 1 ORDER BY organization_name";
$orgResult = mysqli_query($conn, $orgQuery);

// Fetch users
$userQuery = "SELECT p.profile_id, p.profile_email, p.profile_name, oa.organization_id, o.organization_name
              FROM profile p
              LEFT JOIN organization_admins oa ON p.admin_id = oa.admin_id
              LEFT JOIN organizations o ON oa.organization_id = o.organization_id
              WHERE p.profile_role_id != 'gPHOfKV0sL'
              ORDER BY p.profile_name";
$userResult = mysqli_query($conn, $userQuery);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $profileId = $_POST['profile_id'];
    $organizationId = $_POST['organization_id'];

    if (!empty($profileId) && !empty($organizationId)) {
        // Check if assignment already exists
        $checkQuery = "SELECT admin_id FROM organization_admins WHERE profile_id = ? AND organization_id = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("ss", $profileId, $organizationId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            // User already assigned to this organization
            $existing = $result->fetch_assoc();
            $admin_id = $existing['admin_id'];

            // Update profile table with admin_id
            $updateQuery = "UPDATE profile SET admin_id = ? WHERE profile_id = ?";
            $stmt2 = $conn->prepare($updateQuery);
            $stmt2->bind_param("is", $admin_id, $profileId);

            if ($stmt2->execute()) {
                echo "<script>alert('User organization updated successfully!'); window.location.href='user_list.php';</script>";
            } else {
                echo "<script>alert('Failed to update user organization!');</script>";
            }
            $stmt2->close();
        } else {
            // Create new organization assignment
            $insertQuery = "INSERT INTO organization_admins (organization_id, profile_id) VALUES (?, ?)";
            $stmt2 = $conn->prepare($insertQuery);
            $stmt2->bind_param("ss", $organizationId, $profileId);

            if ($stmt2->execute()) {
                $admin_id = $conn->insert_id;

                // Update profile table with admin_id
                $updateQuery = "UPDATE profile SET admin_id = ? WHERE profile_id = ?";
                $stmt3 = $conn->prepare($updateQuery);
                $stmt3->bind_param("is", $admin_id, $profileId);

                if ($stmt3->execute()) {
                    echo "<script>alert('User organization updated successfully!'); window.location.href='user_list.php';</script>";
                } else {
                    echo "<script>alert('Failed to update profile with admin_id!');</script>";
                }
                $stmt3->close();
            } else {
                echo "<script>alert('Failed to assign user to organization!');</script>";
            }
            $stmt2->close();
        }
        $stmt->close();
    } else {
        echo "<script>alert('Please select both user and organization!');</script>";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assign User to Organization</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f3f4f6;
            font-family: 'Inter', sans-serif;
        }

        .main-title {
            font-size: 28px;
            font-weight: 800;
            margin: 20px 0;
            text-align: center;
            color: #1f2937;
        }

        .form-container {
            max-width: 90%;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }

        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: border-color 0.2s ease;
            font-size: 14px;
        }

        .form-group select:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        .btn-submit {
            background-color: #3b82f6;
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            border: none;
            width: 100%;
        }

        .btn-submit:hover {
            background-color: #2563eb;
        }

        .btn-back {
            display: block;
            margin-top: 20px;
            text-align: center;
            color: #3b82f6;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .btn-back:hover {
            color: #2563eb;
        }

        .current-org {
            font-size: 12px;
            color: #6b7280;
            font-style: italic;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <?php include("admin_header.php"); ?>

    <div class="container">
        <h2 class="main-title">Assign User to Organization</h2>
        <div class="form-container">
            <form id="assignUserForm" method="POST" action="assign_user_organization.php">
                <div class="form-group">
                    <label for="profile_id">Select User *</label>
                    <select id="profile_id" name="profile_id" required>
                        <option value="" disabled selected>Select User</option>
                        <?php while ($userRow = mysqli_fetch_assoc($userResult)) { ?>
                            <option value="<?php echo $userRow['profile_id']; ?>">
                                <?php 
                                echo $userRow['profile_name'] . " (" . $userRow['profile_email'] . ")";
                                if (!empty($userRow['organization_name'])) {
                                    echo " - Current: " . $userRow['organization_name'];
                                } else {
                                    echo " - No Organization";
                                }
                                ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="organization_id">Select Organization *</label>
                    <select id="organization_id" name="organization_id" required>
                        <option value="" disabled selected>Select Organization</option>
                        <?php while ($orgRow = mysqli_fetch_assoc($orgResult)) { ?>
                            <option value="<?php echo $orgRow['organization_id']; ?>">
                                <?php echo $orgRow['organization_name']; ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
                
                <button type="submit" class="btn-submit">Assign User to Organization</button>
            </form>
            <a href="user_list.php" class="btn-back">Back to User List</a>
        </div>
    </div>

    <!-- Footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>
</body>
</html>
