<?php
require("../database.php");

// Only master admin can run this migration
session_start();
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_roleid = $_SESSION['profile_role_id'];
    if ($admin_roleid != 'gPHOfKV0sL') {
        die("Access denied. Only master admin can run this migration.");
    }
} else {
    die("Please login as master admin to run this migration.");
}

echo "<h2>Extending Organization Permissions - Adding Category List, User List, and Role List</h2>";

// Fetch all organizations
$query = "SELECT organization_id, organization_name, permission FROM organizations";
$result = mysqli_query($conn, $query);

if (!$result) {
    die("Error fetching organizations: " . mysqli_error($conn));
}

$updated = 0;
$errors = 0;

while ($row = mysqli_fetch_assoc($result)) {
    $orgId = $row['organization_id'];
    $orgName = $row['organization_name'];
    $currentPermissions = $row['permission'];
    
    echo "<p>Processing organization: <strong>$orgName</strong> (ID: $orgId)</p>";
    
    // Parse current JSON permissions
    $permissions = json_decode($currentPermissions, true);
    
    if (!$permissions) {
        echo "<p style='color: red;'>  ❌ Invalid JSON format, skipping...</p>";
        $errors++;
        continue;
    }
    
    // Check if already has the new permission types
    if (isset($permissions['category_list']) && isset($permissions['user_list']) && isset($permissions['role_list'])) {
        echo "<p style='color: blue;'>  ℹ️ Already has extended permissions, skipping...</p>";
        continue;
    }
    
    // Add the new permission types with default CRUD permissions
    $permissions['category_list'] = [
        'create' => true,
        'read' => true,
        'update' => true,
        'delete' => true
    ];
    
    $permissions['user_list'] = [
        'create' => true,
        'read' => true,
        'update' => true,
        'delete' => true
    ];
    
    $permissions['role_list'] = [
        'create' => true,
        'read' => true,
        'update' => true,
        'delete' => true
    ];
    
    // Convert back to JSON
    $newPermissionJson = json_encode($permissions);
    
    // Update the organization
    $updateQuery = "UPDATE organizations SET permission = ? WHERE organization_id = ?";
    $stmt = mysqli_prepare($conn, $updateQuery);
    mysqli_stmt_bind_param($stmt, "ss", $newPermissionJson, $orgId);
    
    if (mysqli_stmt_execute($stmt)) {
        echo "<p style='color: green;'>  ✅ Successfully updated permissions</p>";
        $updated++;
    } else {
        echo "<p style='color: red;'>  ❌ Failed to update: " . mysqli_error($conn) . "</p>";
        $errors++;
    }
    
    mysqli_stmt_close($stmt);
}

echo "<hr>";
echo "<h3>Migration Summary:</h3>";
echo "<p><strong>Organizations updated:</strong> $updated</p>";
echo "<p><strong>Errors:</strong> $errors</p>";

if ($updated > 0) {
    echo "<p style='color: green; font-weight: bold;'>✅ Migration completed successfully!</p>";
    echo "<p>The following permission types have been added:</p>";
    echo "<ul>";
    echo "<li><strong>Category List Management</strong> - Create, Read, Update, Delete</li>";
    echo "<li><strong>User List Management</strong> - Create, Read, Update, Delete</li>";
    echo "<li><strong>Role List Management</strong> - Create, Read, Update, Delete</li>";
    echo "</ul>";
} else {
    echo "<p style='color: orange;'>ℹ️ No organizations needed updating.</p>";
}

echo "<p><a href='organization_list.php'>← Back to Organization List</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Extend Permissions Migration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h2 { color: #333; }
        p { margin: 5px 0; }
        ul { margin: 10px 0; }
        hr { margin: 20px 0; }
    </style>
</head>
<body>
</body>
</html>
