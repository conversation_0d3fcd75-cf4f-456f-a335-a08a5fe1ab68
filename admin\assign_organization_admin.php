<?php
require("../database.php");
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Only master admin can assign organization admins
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

// Fetch organizations
$orgQuery = "SELECT organization_id, organization_name FROM organizations WHERE organization_status = 1 ORDER BY organization_name";
$orgResult = mysqli_query($conn, $orgQuery);

// Fetch users for email selection (only admin and master admin roles)
$userQuery = "SELECT p.profile_id, p.profile_email, p.profile_name, p.profile_role_id, o.organization_name
              FROM profile p
              LEFT JOIN organization_admins oa ON p.role_id = oa.role_id
              LEFT JOIN organizations o ON oa.organization_id = o.organization_id
              WHERE p.profile_role_id IN ('gPHOfKV0sL', 'admin_role_id')
              ORDER BY p.profile_role_id, p.profile_name";
$userResult = mysqli_query($conn, $userQuery);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $profileId = $_POST['profile_id'];
    $organizationId = $_POST['organization_id'];
    $adminName = trim($_POST['admin_name']);

    if (!empty($profileId) && !empty($organizationId) && !empty($adminName)) {
        // Check if assignment already exists
        $checkQuery = "SELECT role_id FROM organization_admins WHERE profile_id = ? AND organization_id = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("ss", $profileId, $organizationId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            echo "<script>alert('User is already an admin for this organization!');</script>";
        } else {
            // Get permission values from form
            $permissions = [
                'event' => [
                    'create' => isset($_POST['event_create']) ? true : false,
                    'read' => isset($_POST['event_read']) ? true : false,
                    'update' => isset($_POST['event_update']) ? true : false,
                    'delete' => isset($_POST['event_delete']) ? true : false
                ],
                'golf_info' => [
                    'create' => isset($_POST['golf_info_create']) ? true : false,
                    'read' => isset($_POST['golf_info_read']) ? true : false,
                    'update' => isset($_POST['golf_info_update']) ? true : false,
                    'delete' => isset($_POST['golf_info_delete']) ? true : false
                ],
                'configure_course' => [
                    'create' => isset($_POST['configure_course_create']) ? true : false,
                    'read' => isset($_POST['configure_course_read']) ? true : false,
                    'update' => isset($_POST['configure_course_update']) ? true : false,
                    'delete' => isset($_POST['configure_course_delete']) ? true : false
                ],
                'category_list' => [
                    'create' => isset($_POST['category_list_create']) ? true : false,
                    'read' => isset($_POST['category_list_read']) ? true : false,
                    'update' => isset($_POST['category_list_update']) ? true : false,
                    'delete' => isset($_POST['category_list_delete']) ? true : false
                ],
                'user_list' => [
                    'create' => isset($_POST['user_list_create']) ? true : false,
                    'read' => isset($_POST['user_list_read']) ? true : false,
                    'update' => isset($_POST['user_list_update']) ? true : false,
                    'delete' => isset($_POST['user_list_delete']) ? true : false
                ],
                'role_list' => [
                    'create' => isset($_POST['role_list_create']) ? true : false,
                    'read' => isset($_POST['role_list_read']) ? true : false,
                    'update' => isset($_POST['role_list_update']) ? true : false,
                    'delete' => isset($_POST['role_list_delete']) ? true : false
                ]
            ];

            $permissions_json = json_encode($permissions);

            // Insert new organization admin assignment with custom role name and permissions
            $insertQuery = "INSERT INTO organization_admins (organization_id, profile_id, role_name, permissions) VALUES (?, ?, ?, ?)";
            $stmt2 = $conn->prepare($insertQuery);
            $stmt2->bind_param("ssss", $organizationId, $profileId, $adminName, $permissions_json);

            if ($stmt2->execute()) {
                $role_id = $conn->insert_id;

                // Update profile table with role_id
                $updateQuery = "UPDATE profile SET role_id = ? WHERE profile_id = ?";
                $stmt3 = $conn->prepare($updateQuery);
                $stmt3->bind_param("is", $role_id, $profileId);

                if ($stmt3->execute()) {
                    echo "<script>alert('Organization admin assigned successfully with name: $adminName'); window.location.href='organization_admin_list.php';</script>";
                } else {
                    echo "<script>alert('Failed to update profile with admin_id!');</script>";
                }
                $stmt3->close();
            } else {
                echo "<script>alert('Failed to assign organization admin!');</script>";
            }
            $stmt2->close();
        }
        $stmt->close();
    } else {
        echo "<script>alert('Please fill in all required fields!');</script>";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assign Organization Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f3f4f6;
            font-family: 'Inter', sans-serif;
        }

        .main-title {
            font-size: 28px;
            font-weight: 800;
            margin: 20px 0;
            text-align: center;
            color: #1f2937;
        }

        .form-container {
            max-width: 90%;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }

        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: border-color 0.2s ease;
            font-size: 14px;
        }

        .form-group select:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        .btn-submit {
            background-color: #3b82f6;
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            border: none;
            width: 100%;
        }

        .btn-submit:hover {
            background-color: #2563eb;
        }

        .btn-back {
            display: block;
            margin-top: 20px;
            text-align: center;
            color: #3b82f6;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .btn-back:hover {
            color: #2563eb;
        }

        .permissions-section {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .permissions-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 15px;
            border-bottom: 2px solid #3b82f6;
            padding-bottom: 8px;
        }

        .permission-group {
            margin-bottom: 20px;
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 15px;
        }

        .permission-group-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .permission-options {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .permission-option {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .permission-option input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #3b82f6;
        }

        .permission-option label {
            font-weight: 500;
            cursor: pointer;
            color: #374151;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <?php include("admin_header.php"); ?>

    <div class="container">
        <h2 class="main-title">Assign Organization Admin</h2>
        <div class="form-container">
            <form id="assignAdminForm" method="POST" action="assign_organization_admin.php">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="profile_id">Select User *</label>
                            <select id="profile_id" name="profile_id" required>
                                <option value="" disabled selected>Select User</option>
                                <?php while ($userRow = mysqli_fetch_assoc($userResult)) {
                                    // Convert profile_role_id to readable role name and show profile name
                                    $roleName = '';
                                    if ($userRow['profile_role_id'] == 'gPHOfKV0sL') {
                                        $roleName = 'Master Admin';
                                    } else {
                                        $roleName = 'Admin';
                                    }

                                    // Display format: "Profile Name (Role)"
                                    $displayText = htmlspecialchars($userRow['profile_name']) . ' (' . $roleName . ')';
                                ?>
                                    <option value="<?php echo $userRow['profile_id']; ?>" data-profile-name="<?php echo htmlspecialchars($userRow['profile_name']); ?>">
                                        <?php echo $displayText; ?>
                                    </option>
                                <?php } ?>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="admin_name">Role Name *</label>
                            <input type="text" id="admin_name" name="admin_name" placeholder="Enter role name" required>
                            <small style="color: #6b7280; font-size: 12px; margin-top: 3px; display: block;">
                                Display name for this admin role
                            </small>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="organization_id">Select Organization *</label>
                    <select id="organization_id" name="organization_id" required>
                        <option value="" disabled selected>Select Organization</option>
                        <?php while ($orgRow = mysqli_fetch_assoc($orgResult)) { ?>
                            <option value="<?php echo $orgRow['organization_id']; ?>">
                                <?php echo $orgRow['organization_name']; ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>

                <div class="permissions-section">
                    <div class="permissions-title">Admin Permissions</div>

                    <div class="permission-group">
                        <div class="permission-group-title">Event Management</div>
                        <div class="permission-options">
                            <div class="permission-option">
                                <input type="checkbox" id="event_create" name="event_create">
                                <label for="event_create">Create</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="event_read" name="event_read">
                                <label for="event_read">Read</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="event_update" name="event_update">
                                <label for="event_update">Update</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="event_delete" name="event_delete">
                                <label for="event_delete">Delete</label>
                            </div>
                        </div>
                    </div>

                    <div class="permission-group">
                        <div class="permission-group-title">Golf Info</div>
                        <div class="permission-options">
                            <div class="permission-option">
                                <input type="checkbox" id="golf_info_create" name="golf_info_create">
                                <label for="golf_info_create">Create</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="golf_info_read" name="golf_info_read">
                                <label for="golf_info_read">Read</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="golf_info_update" name="golf_info_update">
                                <label for="golf_info_update">Update</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="golf_info_delete" name="golf_info_delete">
                                <label for="golf_info_delete">Delete</label>
                            </div>
                        </div>
                    </div>

                    <div class="permission-group">
                        <div class="permission-group-title">Configure Course</div>
                        <div class="permission-options">
                            <div class="permission-option">
                                <input type="checkbox" id="configure_course_create" name="configure_course_create">
                                <label for="configure_course_create">Create</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="configure_course_read" name="configure_course_read">
                                <label for="configure_course_read">Read</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="configure_course_update" name="configure_course_update">
                                <label for="configure_course_update">Update</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="configure_course_delete" name="configure_course_delete">
                                <label for="configure_course_delete">Delete</label>
                            </div>
                        </div>
                    </div>

                    <div class="permission-group">
                        <div class="permission-group-title">Category List</div>
                        <div class="permission-options">
                            <div class="permission-option">
                                <input type="checkbox" id="category_list_create" name="category_list_create">
                                <label for="category_list_create">Create</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="category_list_read" name="category_list_read">
                                <label for="category_list_read">Read</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="category_list_update" name="category_list_update">
                                <label for="category_list_update">Update</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="category_list_delete" name="category_list_delete">
                                <label for="category_list_delete">Delete</label>
                            </div>
                        </div>
                    </div>

                    <div class="permission-group">
                        <div class="permission-group-title">User List</div>
                        <div class="permission-options">
                            <div class="permission-option">
                                <input type="checkbox" id="user_list_create" name="user_list_create">
                                <label for="user_list_create">Create</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="user_list_read" name="user_list_read">
                                <label for="user_list_read">Read</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="user_list_update" name="user_list_update">
                                <label for="user_list_update">Update</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="user_list_delete" name="user_list_delete">
                                <label for="user_list_delete">Delete</label>
                            </div>
                        </div>
                    </div>

                    <div class="permission-group">
                        <div class="permission-group-title">Role List</div>
                        <div class="permission-options">
                            <div class="permission-option">
                                <input type="checkbox" id="role_list_create" name="role_list_create">
                                <label for="role_list_create">Create</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="role_list_read" name="role_list_read">
                                <label for="role_list_read">Read</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="role_list_update" name="role_list_update">
                                <label for="role_list_update">Update</label>
                            </div>
                            <div class="permission-option">
                                <input type="checkbox" id="role_list_delete" name="role_list_delete">
                                <label for="role_list_delete">Delete</label>
                            </div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn-submit">Assign Organization Admin</button>
            </form>
            <a href="organization_admin_list.php" class="btn-back">Back to Organization Admin List</a>
        </div>
    </div>

    <!-- Footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>

    <script>
        // Auto-fill admin name when user is selected, but allow manual editing
        document.getElementById('profile_id').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const adminNameField = document.getElementById('admin_name');

            if (selectedOption.value) {
                // Get profile name from data attribute
                const profileName = selectedOption.getAttribute('data-profile-name');

                // Only auto-fill if the admin name field is empty
                if (!adminNameField.value.trim() && profileName) {
                    adminNameField.value = profileName;
                }
            }
        });

        // Add placeholder text that updates based on selection
        document.getElementById('profile_id').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const adminNameField = document.getElementById('admin_name');

            if (selectedOption.value) {
                const profileName = selectedOption.getAttribute('data-profile-name');
                if (profileName) {
                    adminNameField.placeholder = `Enter role name (suggested: ${profileName})`;
                } else {
                    adminNameField.placeholder = 'Enter role name';
                }
            } else {
                adminNameField.placeholder = 'Enter role name';
            }
        });
    </script>
</body>
</html>
