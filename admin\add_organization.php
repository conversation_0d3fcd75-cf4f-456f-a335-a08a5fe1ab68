<?php
require("../database.php");
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Only master admin can manage organizations
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $organizationName = trim($_POST['organization_name']);
    $organizationDescription = trim($_POST['organization_description']);
    $organizationStatus = $_POST['organization_status'];
    $assignmentDate = $_POST['assignment_date'];
    $durationUnit = $_POST['duration_unit'];
    $durationValue = null;

    // Get duration value if not unlimited
    if ($durationUnit != 'unlimited') {
        $durationValue = intval($_POST['duration_value']);
    }

    // Calculate end date based on duration
    $endDate = null;
    if ($durationUnit == 'months' && $durationValue > 0) {
        $endDate = date('Y-m-d', strtotime($assignmentDate . ' + ' . $durationValue . ' months'));
    } elseif ($durationUnit == 'years' && $durationValue > 0) {
        $endDate = date('Y-m-d', strtotime($assignmentDate . ' + ' . $durationValue . ' years'));
    }
    // For 'unlimited', end_date remains null

    if (!empty($organizationName)) {
        // Generate unique organization ID
        $organizationId = 'ORG_' . strtoupper(substr(md5($organizationName . time()), 0, 8));

        // Check if organization name already exists
        $checkQuery = "SELECT COUNT(*) FROM organizations WHERE organization_name = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("s", $organizationName);
        $stmt->execute();
        $stmt->bind_result($count);
        $stmt->fetch();
        $stmt->close();

        if ($count > 0) {
            echo "<script>alert('Organization name already exists!');</script>";
        } else {
            // Insert new organization with configurable duration fields
            $insertQuery = "INSERT INTO organizations (organization_id, organization_name, organization_description, organization_status, assignment_date, end_date, duration_value, duration_unit) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("sssissis", $organizationId, $organizationName, $organizationDescription, $organizationStatus, $assignmentDate, $endDate, $durationValue, $durationUnit);

            if ($stmt->execute()) {
                $durationText = $durationUnit == 'unlimited' ? 'unlimited' : $durationValue . ' ' . $durationUnit;
                echo "<script>alert('Organization added successfully with duration: $durationText'); window.location.href='organization_list.php';</script>";
            } else {
                echo "<script>alert('Failed to add organization!');</script>";
            }
            $stmt->close();
        }
    } else {
        echo "<script>alert('Please fill in all required fields!');</script>";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Organization</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f3f4f6;
            font-family: 'Inter', sans-serif;
        }

        .main-title {
            font-size: 28px;
            font-weight: 800;
            margin: 20px 0;
            text-align: center;
            color: #1f2937;
        }

        .form-container {
            max-width: 90%;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: border-color 0.2s ease;
            font-size: 14px;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        .btn-submit {
            background-color: #3b82f6;
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            border: none;
            width: 100%;
        }

        .btn-submit:hover {
            background-color: #2563eb;
        }

        .btn-back {
            display: block;
            margin-top: 20px;
            text-align: center;
            color: #3b82f6;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .btn-back:hover {
            color: #2563eb;
        }

        .date-section {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f9fafb;
        }

        .date-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #1f2937;
            font-size: 16px;
        }

        .date-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .duration-config {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .duration-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .duration-input-group input[type="number"] {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            width: 80px;
        }

        .duration-input-group select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background-color: white;
            min-width: 120px;
        }

        .duration-examples {
            color: #6b7280;
            font-style: italic;
        }

        .duration-input-group input[type="number"]:disabled {
            background-color: #f3f4f6;
            color: #9ca3af;
        }

        .date-info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-size: 14px;
            color: #1e40af;
        }

        @media (max-width: 768px) {
            .date-row {
                grid-template-columns: 1fr;
            }

            .duration-options {
                flex-direction: column;
                align-items: flex-start;
            }
        }


    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <?php include("admin_header.php"); ?>

    <div class="container">
        <h2 class="main-title">Add New Organization</h2>
        <div class="form-container">
            <form id="addOrganizationForm" method="POST" action="add_organization.php">
                <div class="form-group">
                    <label for="organization_name">Organization Name *</label>
                    <input type="text" id="organization_name" name="organization_name" required>
                </div>
                
                <div class="form-group">
                    <label for="organization_description">Description</label>
                    <textarea id="organization_description" name="organization_description" rows="4" placeholder="Enter organization description..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="organization_status">Status</label>
                    <select id="organization_status" name="organization_status" required>
                        <option value="1" selected>Active</option>
                        <option value="0">Inactive</option>
                    </select>
                </div>

                <div class="date-section">
                    <div class="date-title">Organization Assignment Period</div>

                    <div class="date-row">
                        <div class="form-group">
                            <label for="duration_unit">Duration Type *</label>
                            <div class="duration-config">
                                <div class="duration-input-group">
                                    <input type="number" id="duration_value" name="duration_value" min="1" max="99" placeholder="Number" style="width: 80px;">
                                    <select id="duration_unit" name="duration_unit" required>
                                        <option value="months">Months</option>
                                        <option value="years">Years</option>
                                        <option value="unlimited" selected>Unlimited</option>
                                    </select>
                                </div>
                                <div class="duration-examples">
                                    <small>Examples: 3 months, 2 years, unlimited</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="assignment_date">Assignment Date *</label>
                            <input type="date" id="assignment_date" name="assignment_date" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                    </div>

                    <div class="date-info">
                        <strong>Note:</strong>
                        <span id="duration-info">This organization will have unlimited access.</span>
                    </div>
                </div>



                <button type="submit" class="btn-submit">Add Organization</button>
            </form>
            <a href="organization_list.php" class="btn-back">Back to Organization List</a>
        </div>
    </div>

    <!-- Footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>

    <script>
        // Update duration info and handle configurable duration
        document.addEventListener('DOMContentLoaded', function() {
            const durationUnitSelect = document.getElementById('duration_unit');
            const durationValueInput = document.getElementById('duration_value');
            const durationInfo = document.getElementById('duration-info');
            const assignmentDateInput = document.getElementById('assignment_date');

            function updateDurationInfo() {
                const durationUnit = durationUnitSelect.value;
                const durationValue = parseInt(durationValueInput.value) || 0;
                const assignmentDate = assignmentDateInput.value;

                // Enable/disable duration value input
                if (durationUnit === 'unlimited') {
                    durationValueInput.disabled = true;
                    durationValueInput.value = '';
                    durationInfo.textContent = 'This organization will have unlimited access.';
                } else {
                    durationValueInput.disabled = false;
                    durationValueInput.required = true;

                    if (durationValue > 0 && assignmentDate) {
                        const endDate = new Date(assignmentDate);

                        if (durationUnit === 'months') {
                            endDate.setMonth(endDate.getMonth() + durationValue);
                        } else if (durationUnit === 'years') {
                            endDate.setFullYear(endDate.getFullYear() + durationValue);
                        }

                        const durationText = durationValue + ' ' + durationUnit;
                        durationInfo.textContent = `This organization will have access for ${durationText} and expire on ${endDate.toLocaleDateString()}.`;
                    } else if (durationValue > 0) {
                        const durationText = durationValue + ' ' + durationUnit;
                        durationInfo.textContent = `This organization will have access for ${durationText} from the assignment date.`;
                    } else {
                        durationInfo.textContent = `Please enter the number of ${durationUnit}.`;
                    }
                }
            }

            // Add event listeners
            durationUnitSelect.addEventListener('change', updateDurationInfo);
            durationValueInput.addEventListener('input', updateDurationInfo);
            assignmentDateInput.addEventListener('change', updateDurationInfo);

            // Form validation
            document.getElementById('addOrganizationForm').addEventListener('submit', function(e) {
                const durationUnit = durationUnitSelect.value;
                const durationValue = parseInt(durationValueInput.value) || 0;

                if (durationUnit !== 'unlimited' && durationValue <= 0) {
                    e.preventDefault();
                    alert('Please enter a valid duration value (greater than 0).');
                    durationValueInput.focus();
                    return false;
                }
            });

            // Initial update
            updateDurationInfo();
        });
    </script>
</body>
</html>
