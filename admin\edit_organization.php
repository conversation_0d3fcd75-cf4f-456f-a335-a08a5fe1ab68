<?php
require("../database.php");
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Only master admin can edit organizations
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

$organizationId = $_GET['id'] ?? '';
if (empty($organizationId)) {
    header("Location: organization_list.php");
    exit();
}

// Fetch organization data
$query = "SELECT * FROM organizations WHERE organization_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $organizationId);
$stmt->execute();
$result = $stmt->get_result();
$organization = $result->fetch_assoc();
$stmt->close();

if (!$organization) {
    header("Location: organization_list.php");
    exit();
}

// Parse JSON permissions
$permissions = json_decode($organization['permission'], true);
if (!$permissions) {
    // Default permissions if JSON is invalid
    $permissions = [
        'event' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
        'golf_info' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
        'configure_course' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
        'category_list' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
        'user_list' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
        'role_list' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true]
    ];
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get permission values and create extended JSON format
    $permissions = [
        'event' => [
            'create' => isset($_POST['event_create']) ? true : false,
            'read' => isset($_POST['event_read']) ? true : false,
            'update' => isset($_POST['event_update']) ? true : false,
            'delete' => isset($_POST['event_delete']) ? true : false
        ],
        'golf_info' => [
            'create' => isset($_POST['golf_info_create']) ? true : false,
            'read' => isset($_POST['golf_info_read']) ? true : false,
            'update' => isset($_POST['golf_info_update']) ? true : false,
            'delete' => isset($_POST['golf_info_delete']) ? true : false
        ],
        'configure_course' => [
            'create' => isset($_POST['configure_course_create']) ? true : false,
            'read' => isset($_POST['configure_course_read']) ? true : false,
            'update' => isset($_POST['configure_course_update']) ? true : false,
            'delete' => isset($_POST['configure_course_delete']) ? true : false
        ],
        'category_list' => [
            'create' => isset($_POST['category_list_create']) ? true : false,
            'read' => isset($_POST['category_list_read']) ? true : false,
            'update' => isset($_POST['category_list_update']) ? true : false,
            'delete' => isset($_POST['category_list_delete']) ? true : false
        ],
        'user_list' => [
            'create' => isset($_POST['user_list_create']) ? true : false,
            'read' => isset($_POST['user_list_read']) ? true : false,
            'update' => isset($_POST['user_list_update']) ? true : false,
            'delete' => isset($_POST['user_list_delete']) ? true : false
        ],
        'role_list' => [
            'create' => isset($_POST['role_list_create']) ? true : false,
            'read' => isset($_POST['role_list_read']) ? true : false,
            'update' => isset($_POST['role_list_update']) ? true : false,
            'delete' => isset($_POST['role_list_delete']) ? true : false
        ]
    ];
    $permissionJson = json_encode($permissions);

    // Update only permissions (not organization details)
    $updateQuery = "UPDATE organizations SET permission = ? WHERE organization_id = ?";
    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param("ss", $permissionJson, $organizationId);

    if ($stmt->execute()) {
        echo "<script>alert('Organization permissions updated successfully!'); window.location.href='organization_list.php';</script>";
    } else {
        echo "<script>alert('Failed to update organization permissions!');</script>";
    }
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Organization Permissions</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f3f4f6;
            font-family: 'Inter', sans-serif;
        }

        .main-title {
            font-size: 28px;
            font-weight: 800;
            margin: 20px 0;
            text-align: center;
            color: #1f2937;
        }

        .form-container {
            max-width: 90%;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: border-color 0.2s ease;
            font-size: 14px;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        .btn-submit {
            background-color: #3b82f6;
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            border: none;
            width: 100%;
        }

        .btn-submit:hover {
            background-color: #2563eb;
        }

        .btn-back {
            display: block;
            margin-top: 20px;
            text-align: center;
            color: #3b82f6;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .btn-back:hover {
            color: #2563eb;
        }

        .permissions-section {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f9fafb;
        }

        .permissions-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #1f2937;
            font-size: 16px;
        }

        .permission-group {
            margin-bottom: 20px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 15px;
            background-color: #ffffff;
        }

        .permission-group-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #374151;
            font-size: 14px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }

        .permission-crud-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }

        .permission-item {
            display: flex;
            align-items: center;
            white-space: nowrap;
        }

        .permission-item input[type="checkbox"] {
            margin-right: 6px;
            width: 16px;
            height: 16px;
        }

        .permission-item label {
            margin-bottom: 0;
            font-weight: 500;
            cursor: pointer;
            font-size: 13px;
        }

        .info-section {
            background-color: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-title {
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 5px;
        }

        .info-text {
            color: #1e40af;
            font-size: 14px;
        }


    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <?php include("admin_header.php"); ?>

    <div class="container">
        <h2 class="main-title">Edit Organization Permissions</h2>
        <div class="info-section">
            <div class="info-title">Organization: <?php echo htmlspecialchars($organization['organization_name']); ?></div>
            <div class="info-text">Manage permissions for this organization. To edit organization details (name, description, status), click the organization name in the list.</div>
        </div>

        <div class="form-container">
            <form id="editOrganizationPermissionsForm" method="POST" action="edit_organization.php?id=<?php echo $organizationId; ?>">

                <div class="permissions-section">
                    <div class="permissions-title">Organization Permissions</div>

                    <!-- Event Permissions -->
                    <div class="permission-group">
                        <div class="permission-group-title">Event Management</div>
                        <div class="permission-crud-row">
                            <div class="permission-item">
                                <input type="checkbox" id="event_create" name="event_create" <?php echo ($permissions['event']['create']) ? 'checked' : ''; ?>>
                                <label for="event_create">Create</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="event_read" name="event_read" <?php echo ($permissions['event']['read']) ? 'checked' : ''; ?>>
                                <label for="event_read">Read</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="event_update" name="event_update" <?php echo ($permissions['event']['update']) ? 'checked' : ''; ?>>
                                <label for="event_update">Update</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="event_delete" name="event_delete" <?php echo ($permissions['event']['delete']) ? 'checked' : ''; ?>>
                                <label for="event_delete">Delete</label>
                            </div>
                        </div>
                    </div>

                    <!-- Golf Info Permissions -->
                    <div class="permission-group">
                        <div class="permission-group-title">Golf Info Management</div>
                        <div class="permission-crud-row">
                            <div class="permission-item">
                                <input type="checkbox" id="golf_info_create" name="golf_info_create" <?php echo ($permissions['golf_info']['create']) ? 'checked' : ''; ?>>
                                <label for="golf_info_create">Create</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="golf_info_read" name="golf_info_read" <?php echo ($permissions['golf_info']['read']) ? 'checked' : ''; ?>>
                                <label for="golf_info_read">Read</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="golf_info_update" name="golf_info_update" <?php echo ($permissions['golf_info']['update']) ? 'checked' : ''; ?>>
                                <label for="golf_info_update">Update</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="golf_info_delete" name="golf_info_delete" <?php echo ($permissions['golf_info']['delete']) ? 'checked' : ''; ?>>
                                <label for="golf_info_delete">Delete</label>
                            </div>
                        </div>
                    </div>

                    <!-- Configure Course Permissions -->
                    <div class="permission-group">
                        <div class="permission-group-title">Configure Course Management</div>
                        <div class="permission-crud-row">
                            <div class="permission-item">
                                <input type="checkbox" id="configure_course_create" name="configure_course_create" <?php echo ($permissions['configure_course']['create']) ? 'checked' : ''; ?>>
                                <label for="configure_course_create">Create</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="configure_course_read" name="configure_course_read" <?php echo ($permissions['configure_course']['read']) ? 'checked' : ''; ?>>
                                <label for="configure_course_read">Read</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="configure_course_update" name="configure_course_update" <?php echo ($permissions['configure_course']['update']) ? 'checked' : ''; ?>>
                                <label for="configure_course_update">Update</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="configure_course_delete" name="configure_course_delete" <?php echo ($permissions['configure_course']['delete']) ? 'checked' : ''; ?>>
                                <label for="configure_course_delete">Delete</label>
                            </div>
                        </div>
                    </div>

                    <!-- Category List Permissions -->
                    <div class="permission-group">
                        <div class="permission-group-title">Category List Management</div>
                        <div class="permission-crud-row">
                            <div class="permission-item">
                                <input type="checkbox" id="category_list_create" name="category_list_create" <?php echo ($permissions['category_list']['create']) ? 'checked' : ''; ?>>
                                <label for="category_list_create">Create</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="category_list_read" name="category_list_read" <?php echo ($permissions['category_list']['read']) ? 'checked' : ''; ?>>
                                <label for="category_list_read">Read</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="category_list_update" name="category_list_update" <?php echo ($permissions['category_list']['update']) ? 'checked' : ''; ?>>
                                <label for="category_list_update">Update</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="category_list_delete" name="category_list_delete" <?php echo ($permissions['category_list']['delete']) ? 'checked' : ''; ?>>
                                <label for="category_list_delete">Delete</label>
                            </div>
                        </div>
                    </div>

                    <!-- User List Permissions -->
                    <div class="permission-group">
                        <div class="permission-group-title">User List Management</div>
                        <div class="permission-crud-row">
                            <div class="permission-item">
                                <input type="checkbox" id="user_list_create" name="user_list_create" <?php echo ($permissions['user_list']['create']) ? 'checked' : ''; ?>>
                                <label for="user_list_create">Create</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="user_list_read" name="user_list_read" <?php echo ($permissions['user_list']['read']) ? 'checked' : ''; ?>>
                                <label for="user_list_read">Read</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="user_list_update" name="user_list_update" <?php echo ($permissions['user_list']['update']) ? 'checked' : ''; ?>>
                                <label for="user_list_update">Update</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="user_list_delete" name="user_list_delete" <?php echo ($permissions['user_list']['delete']) ? 'checked' : ''; ?>>
                                <label for="user_list_delete">Delete</label>
                            </div>
                        </div>
                    </div>

                    <!-- Role List Permissions -->
                    <div class="permission-group">
                        <div class="permission-group-title">Role List Management</div>
                        <div class="permission-crud-row">
                            <div class="permission-item">
                                <input type="checkbox" id="role_list_create" name="role_list_create" <?php echo ($permissions['role_list']['create']) ? 'checked' : ''; ?>>
                                <label for="role_list_create">Create</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="role_list_read" name="role_list_read" <?php echo ($permissions['role_list']['read']) ? 'checked' : ''; ?>>
                                <label for="role_list_read">Read</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="role_list_update" name="role_list_update" <?php echo ($permissions['role_list']['update']) ? 'checked' : ''; ?>>
                                <label for="role_list_update">Update</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="role_list_delete" name="role_list_delete" <?php echo ($permissions['role_list']['delete']) ? 'checked' : ''; ?>>
                                <label for="role_list_delete">Delete</label>
                            </div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn-submit">Update Permissions</button>
            </form>
            <a href="organization_list.php" class="btn-back">Back to Organization List</a>
            <a href="edit_organization_details.php?id=<?php echo $organizationId; ?>" class="btn-back">Edit Organization Details</a>
        </div>
    </div>

    <!-- Footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>
</body>
</html>
