<?php
require("../database.php");
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Only master admin can manage organizations
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

// Fetch organizations with configurable duration fields
$query = "
    SELECT
        o.organization_id,
        o.organization_name,
        o.organization_description,
        o.assignment_date,
        o.end_date,
        o.duration_value,
        o.duration_unit,
        o.organization_status
    FROM organizations o
    ORDER BY o.organization_name
";
$result = mysqli_query($conn, $query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organization Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.8.1/font/bootstrap-icons.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f3f4f6;
            font-family: 'Inter', sans-serif;
        }

        .main-title {
            font-size: 36px;
            font-weight: 800;
            margin: 30px 0;
            text-align: center;
            color: #1f2937;
        }

        .header-section {
            background-color: #ffffff;
            padding: 25px 0;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 30px;
            box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
        }

        .table-container {
            margin-top: 20px;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.05);
            overflow-x: auto;
        }

        .table th, .table td {
            padding: 16px;
            text-align: left;
            vertical-align: middle;
            font-size: 16px;
            border: none;
        }

        .table th {
            background-color: #f9fafb;
            font-weight: 700;
            color: #1f2937;
        }

        .table tbody tr {
            border-bottom: 1px solid #e5e7eb;
        }

        .table tbody tr:hover {
            background-color: #f3f4f6;
        }

        .status-active {
            color: #10b981 !important;
            font-weight: bold;
        }

        .status-inactive {
            color: #ef4444 !important;
            font-weight: bold;
        }

        .icon {
            font-size: 20px;
            cursor: pointer;
            margin: 0 5px;
        }

        .icon.edit-icon {
            color: #3b82f6;
            font-size: 24px;
        }

        .icon.delete-icon {
            color: #ef4444;
            font-size: 24px;
        }

        .icon:hover {
            opacity: 0.7;
        }



        .organization-name-link {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.2s ease;
        }

        .organization-name-link:hover {
            color: #2563eb;
            text-decoration: underline;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            align-items: center;
        }

        td:last-child {
            text-align: center;
        }

        .icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 16px;
        }



        .delete-icon {
            background-color: #ef4444;
        }

        .delete-icon:hover {
            background-color: #dc2626;
            transform: scale(1.1);
        }
    </style>
</head>
<body class="bg-gray-100">

    <!-- Header -->
    <?php include("admin_header.php"); ?>

    <!-- Header Section -->
    <div class="header-section">
        <h2 class="main-title">Organization Management</h2>
    </div>

    <!-- Add New Button -->
    <div class="container mx-auto px-4">
        <div class="d-flex justify-content-between mb-6">
            <div class="search-box me-3" style="flex-grow: 1;">
                <input type="text" id="searchBox" class="form-control" placeholder="Search Organizations...">
            </div>
            <a href="add_organization.php" class="btn btn-primary">Add Organization</a>
        </div>

        <!-- Organizations Table -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>No.</th>
                        <th>Organization Name</th>
                        <th>Description</th>
                        <th>Assignment Date</th>
                        <th>End Date</th>
                        <th>Duration</th>
                        <th>Status</th>
                        <th style="text-align: center;">Action</th>
                    </tr>
                </thead>
                <tbody id="organizationTable">
                    <?php
                    $counter = 1;
                    while ($row = mysqli_fetch_assoc($result)) {
                        $statusText = $row['organization_status'] == 1 ? 'Active' : 'Inactive';
                        $statusClass = $row['organization_status'] == 1 ? 'status-active' : 'status-inactive';

                        // Format dates in full format (date month year)
                        $assignmentDate = $row['assignment_date'] ? date('d F Y', strtotime($row['assignment_date'])) : 'N/A';
                        $endDate = $row['end_date'] ? date('d F Y', strtotime($row['end_date'])) : 'N/A';

                        // Format configurable duration
                        $durationType = $row['duration_unit'] == 'unlimited' ? 'Unlimited' : $row['duration_value'] . ' ' . ucfirst($row['duration_unit']);

                        echo "<tr>
                                <td>{$counter}</td>
                                <td>
                                    <a href='edit_organization_details.php?id={$row['organization_id']}' class='organization-name-link'>
                                        {$row['organization_name']}
                                    </a>
                                </td>
                                <td>" . substr($row['organization_description'], 0, 50) . "...</td>
                                <td>{$assignmentDate}</td>
                                <td>{$endDate}</td>
                                <td>{$durationType}</td>
                                <td class='{$statusClass}'>{$statusText}</td>
                                <td>
                                    <div class='action-buttons'>";

                        // All organizations can be deleted
                        echo "<a href='delete_organization.php?id={$row['organization_id']}' class='icon delete-icon' onclick='return confirm(\"Are you sure you want to delete this organization?\")' title='Delete Organization'>
                                🗑️
                              </a>";

                        echo "    </div>
                                </td>
                            </tr>";
                        $counter++;
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const organizationTable = document.getElementById('organizationTable');
            const searchBox = document.getElementById('searchBox');

            // Search Organizations
            searchBox.addEventListener('input', () => {
                const searchTerm = searchBox.value.toLowerCase();
                const rows = organizationTable.querySelectorAll('tr');

                rows.forEach(row => {
                    const rowText = row.textContent.toLowerCase();
                    row.style.display = rowText.includes(searchTerm) ? '' : 'none';
                });
            });
        });
    </script>
</body>
</html>
