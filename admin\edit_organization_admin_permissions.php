<?php
require("../database.php");
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Only master admin can manage organization admin permissions
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

$role_id = isset($_GET['role_id']) ? $_GET['role_id'] : '';

if (!$role_id) {
    header("Location: organization_admin_list.php?error=invalid_role_id");
    exit();
}

// Fetch organization admin details
$query = "SELECT oa.role_id, oa.organization_id, oa.profile_id, oa.permissions, oa.role_name,
                 o.organization_name, p.profile_name, p.profile_email
          FROM organization_admins oa
          INNER JOIN organizations o ON oa.organization_id = o.organization_id
          INNER JOIN profile p ON oa.profile_id = p.profile_id
          WHERE oa.role_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $role_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    header("Location: organization_admin_list.php?error=admin_not_found");
    exit();
}

$admin_data = $result->fetch_assoc();
$stmt->close();

// Parse JSON permissions
$permissions = json_decode($admin_data['permissions'], true);
if (!$permissions) {
    // Default permissions if JSON is invalid
    $permissions = [
        'event' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
        'golf_info' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
        'configure_course' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
        'category_list' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
        'user_list' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true],
        'role_list' => ['create' => true, 'read' => true, 'update' => true, 'delete' => true]
    ];
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get permission values and create extended JSON format
    $permissions = [
        'event' => [
            'create' => isset($_POST['event_create']) ? true : false,
            'read' => isset($_POST['event_read']) ? true : false,
            'update' => isset($_POST['event_update']) ? true : false,
            'delete' => isset($_POST['event_delete']) ? true : false
        ],
        'golf_info' => [
            'create' => isset($_POST['golf_info_create']) ? true : false,
            'read' => isset($_POST['golf_info_read']) ? true : false,
            'update' => isset($_POST['golf_info_update']) ? true : false,
            'delete' => isset($_POST['golf_info_delete']) ? true : false
        ],
        'configure_course' => [
            'create' => isset($_POST['configure_course_create']) ? true : false,
            'read' => isset($_POST['configure_course_read']) ? true : false,
            'update' => isset($_POST['configure_course_update']) ? true : false,
            'delete' => isset($_POST['configure_course_delete']) ? true : false
        ],
        'category_list' => [
            'create' => isset($_POST['category_list_create']) ? true : false,
            'read' => isset($_POST['category_list_read']) ? true : false,
            'update' => isset($_POST['category_list_update']) ? true : false,
            'delete' => isset($_POST['category_list_delete']) ? true : false
        ],
        'user_list' => [
            'create' => isset($_POST['user_list_create']) ? true : false,
            'read' => isset($_POST['user_list_read']) ? true : false,
            'update' => isset($_POST['user_list_update']) ? true : false,
            'delete' => isset($_POST['user_list_delete']) ? true : false
        ],
        'role_list' => [
            'create' => isset($_POST['role_list_create']) ? true : false,
            'read' => isset($_POST['role_list_read']) ? true : false,
            'update' => isset($_POST['role_list_update']) ? true : false,
            'delete' => isset($_POST['role_list_delete']) ? true : false
        ]
    ];

    $permissions_json = json_encode($permissions);

    // Update organization admin permissions
    $update_query = "UPDATE organization_admins SET permissions = ? WHERE role_id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("si", $permissions_json, $role_id);

    if ($update_stmt->execute()) {
        $success_message = "Organization admin permissions updated successfully!";
    } else {
        $error_message = "Error updating permissions: " . $conn->error;
    }
    $update_stmt->close();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Organization Admin Permissions</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8fafc;
        }

        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .main-title {
            font-size: 2rem;
            font-weight: bold;
            text-align: center;
            margin: 0;
        }

        .form-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin: 2rem auto;
            max-width: 800px;
        }

        .permission-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background-color: #f9fafb;
        }

        .permission-title {
            font-size: 1.25rem;
            font-weight: bold;
            color: #374151;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .permission-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
        }

        .permission-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .permission-checkbox {
            width: 18px;
            height: 18px;
            accent-color: #3b82f6;
        }

        .permission-label {
            font-size: 0.875rem;
            color: #4b5563;
            cursor: pointer;
        }

        .btn-save {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-cancel {
            background: #6b7280;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-cancel:hover {
            background: #4b5563;
            color: white;
            text-decoration: none;
        }

        .admin-info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }

        .alert-error {
            background-color: #fee2e2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }
    </style>
</head>
<body>

    <!-- Header -->
    <?php include("admin_header.php"); ?>

    <!-- Header Section -->
    <div class="header-section">
        <h2 class="main-title">Edit Organization Admin Permissions</h2>
    </div>

    <div class="container">
        <div class="form-container">
            
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <!-- Admin Information -->
            <div class="admin-info">
                <h4><i class="fas fa-user-shield"></i> Admin Information</h4>
                <p><strong>Role Name:</strong> <?php echo htmlspecialchars(!empty($admin_data['role_name']) ? $admin_data['role_name'] : $admin_data['profile_name']); ?></p>
                <p><strong>Profile Name:</strong> <?php echo htmlspecialchars($admin_data['profile_name']); ?></p>
                <p><strong>Email:</strong> <?php echo htmlspecialchars($admin_data['profile_email']); ?></p>
                <p><strong>Organization:</strong> <?php echo htmlspecialchars($admin_data['organization_name']); ?></p>
            </div>

            <form method="POST" action="">
                
                <!-- Event Management Permissions -->
                <div class="permission-section">
                    <div class="permission-title">
                        <i class="fas fa-calendar-alt"></i> Event Management
                    </div>
                    <div class="permission-grid">
                        <div class="permission-item">
                            <input type="checkbox" id="event_create" name="event_create" class="permission-checkbox" 
                                   <?php echo ($permissions['event']['create'] ?? false) ? 'checked' : ''; ?>>
                            <label for="event_create" class="permission-label">Create</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="event_read" name="event_read" class="permission-checkbox" 
                                   <?php echo ($permissions['event']['read'] ?? false) ? 'checked' : ''; ?>>
                            <label for="event_read" class="permission-label">Read</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="event_update" name="event_update" class="permission-checkbox" 
                                   <?php echo ($permissions['event']['update'] ?? false) ? 'checked' : ''; ?>>
                            <label for="event_update" class="permission-label">Update</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="event_delete" name="event_delete" class="permission-checkbox" 
                                   <?php echo ($permissions['event']['delete'] ?? false) ? 'checked' : ''; ?>>
                            <label for="event_delete" class="permission-label">Delete</label>
                        </div>
                    </div>
                </div>

                <!-- Golf Info Permissions -->
                <div class="permission-section">
                    <div class="permission-title">
                        <i class="fas fa-golf-ball"></i> Golf Info Management
                    </div>
                    <div class="permission-grid">
                        <div class="permission-item">
                            <input type="checkbox" id="golf_info_create" name="golf_info_create" class="permission-checkbox" 
                                   <?php echo ($permissions['golf_info']['create'] ?? false) ? 'checked' : ''; ?>>
                            <label for="golf_info_create" class="permission-label">Create</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="golf_info_read" name="golf_info_read" class="permission-checkbox" 
                                   <?php echo ($permissions['golf_info']['read'] ?? false) ? 'checked' : ''; ?>>
                            <label for="golf_info_read" class="permission-label">Read</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="golf_info_update" name="golf_info_update" class="permission-checkbox" 
                                   <?php echo ($permissions['golf_info']['update'] ?? false) ? 'checked' : ''; ?>>
                            <label for="golf_info_update" class="permission-label">Update</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="golf_info_delete" name="golf_info_delete" class="permission-checkbox" 
                                   <?php echo ($permissions['golf_info']['delete'] ?? false) ? 'checked' : ''; ?>>
                            <label for="golf_info_delete" class="permission-label">Delete</label>
                        </div>
                    </div>
                </div>

                <!-- Configure Course Permissions -->
                <div class="permission-section">
                    <div class="permission-title">
                        <i class="fas fa-cog"></i> Configure Course
                    </div>
                    <div class="permission-grid">
                        <div class="permission-item">
                            <input type="checkbox" id="configure_course_create" name="configure_course_create" class="permission-checkbox" 
                                   <?php echo ($permissions['configure_course']['create'] ?? false) ? 'checked' : ''; ?>>
                            <label for="configure_course_create" class="permission-label">Create</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="configure_course_read" name="configure_course_read" class="permission-checkbox" 
                                   <?php echo ($permissions['configure_course']['read'] ?? false) ? 'checked' : ''; ?>>
                            <label for="configure_course_read" class="permission-label">Read</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="configure_course_update" name="configure_course_update" class="permission-checkbox" 
                                   <?php echo ($permissions['configure_course']['update'] ?? false) ? 'checked' : ''; ?>>
                            <label for="configure_course_update" class="permission-label">Update</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="configure_course_delete" name="configure_course_delete" class="permission-checkbox" 
                                   <?php echo ($permissions['configure_course']['delete'] ?? false) ? 'checked' : ''; ?>>
                            <label for="configure_course_delete" class="permission-label">Delete</label>
                        </div>
                    </div>
                </div>

                <!-- Category List Permissions -->
                <div class="permission-section">
                    <div class="permission-title">
                        <i class="fas fa-list"></i> Category List Management
                    </div>
                    <div class="permission-grid">
                        <div class="permission-item">
                            <input type="checkbox" id="category_list_create" name="category_list_create" class="permission-checkbox"
                                   <?php echo ($permissions['category_list']['create'] ?? false) ? 'checked' : ''; ?>>
                            <label for="category_list_create" class="permission-label">Create</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="category_list_read" name="category_list_read" class="permission-checkbox"
                                   <?php echo ($permissions['category_list']['read'] ?? false) ? 'checked' : ''; ?>>
                            <label for="category_list_read" class="permission-label">Read</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="category_list_update" name="category_list_update" class="permission-checkbox"
                                   <?php echo ($permissions['category_list']['update'] ?? false) ? 'checked' : ''; ?>>
                            <label for="category_list_update" class="permission-label">Update</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="category_list_delete" name="category_list_delete" class="permission-checkbox"
                                   <?php echo ($permissions['category_list']['delete'] ?? false) ? 'checked' : ''; ?>>
                            <label for="category_list_delete" class="permission-label">Delete</label>
                        </div>
                    </div>
                </div>

                <!-- User List Permissions -->
                <div class="permission-section">
                    <div class="permission-title">
                        <i class="fas fa-users"></i> User List Management
                    </div>
                    <div class="permission-grid">
                        <div class="permission-item">
                            <input type="checkbox" id="user_list_create" name="user_list_create" class="permission-checkbox"
                                   <?php echo ($permissions['user_list']['create'] ?? false) ? 'checked' : ''; ?>>
                            <label for="user_list_create" class="permission-label">Create</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="user_list_read" name="user_list_read" class="permission-checkbox"
                                   <?php echo ($permissions['user_list']['read'] ?? false) ? 'checked' : ''; ?>>
                            <label for="user_list_read" class="permission-label">Read</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="user_list_update" name="user_list_update" class="permission-checkbox"
                                   <?php echo ($permissions['user_list']['update'] ?? false) ? 'checked' : ''; ?>>
                            <label for="user_list_update" class="permission-label">Update</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="user_list_delete" name="user_list_delete" class="permission-checkbox"
                                   <?php echo ($permissions['user_list']['delete'] ?? false) ? 'checked' : ''; ?>>
                            <label for="user_list_delete" class="permission-label">Delete</label>
                        </div>
                    </div>
                </div>

                <!-- Role List Permissions -->
                <div class="permission-section">
                    <div class="permission-title">
                        <i class="fas fa-user-tag"></i> Role List Management
                    </div>
                    <div class="permission-grid">
                        <div class="permission-item">
                            <input type="checkbox" id="role_list_create" name="role_list_create" class="permission-checkbox"
                                   <?php echo ($permissions['role_list']['create'] ?? false) ? 'checked' : ''; ?>>
                            <label for="role_list_create" class="permission-label">Create</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="role_list_read" name="role_list_read" class="permission-checkbox"
                                   <?php echo ($permissions['role_list']['read'] ?? false) ? 'checked' : ''; ?>>
                            <label for="role_list_read" class="permission-label">Read</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="role_list_update" name="role_list_update" class="permission-checkbox"
                                   <?php echo ($permissions['role_list']['update'] ?? false) ? 'checked' : ''; ?>>
                            <label for="role_list_update" class="permission-label">Update</label>
                        </div>
                        <div class="permission-item">
                            <input type="checkbox" id="role_list_delete" name="role_list_delete" class="permission-checkbox"
                                   <?php echo ($permissions['role_list']['delete'] ?? false) ? 'checked' : ''; ?>>
                            <label for="role_list_delete" class="permission-label">Delete</label>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-between mt-4">
                    <a href="organization_admin_list.php" class="btn-cancel">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                    <button type="submit" class="btn-save">
                        <i class="fas fa-save"></i> Save Permissions
                    </button>
                </div>

            </form>
        </div>
    </div>

    <!-- Footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>

</body>
</html>
