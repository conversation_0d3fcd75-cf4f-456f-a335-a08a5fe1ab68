<?php
require("../database.php");
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Only master admin can delete organizations
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

$organizationId = $_GET['id'] ?? '';
if (empty($organizationId)) {
    header("Location: organization_list.php");
    exit();
}

// Allow deletion of all organizations (including master organization)

// Check if organization has users or events
$userCountQuery = "SELECT COUNT(*) FROM profile WHERE organization_id = ?";
$stmt = $conn->prepare($userCountQuery);
$stmt->bind_param("s", $organizationId);
$stmt->execute();
$stmt->bind_result($userCount);
$stmt->fetch();
$stmt->close();

$eventCountQuery = "SELECT COUNT(*) FROM event_mgm WHERE organization_id = ?";
$stmt = $conn->prepare($eventCountQuery);
$stmt->bind_param("s", $organizationId);
$stmt->execute();
$stmt->bind_result($eventCount);
$stmt->fetch();
$stmt->close();

if ($userCount > 0 || $eventCount > 0) {
    echo "<script>alert('Cannot delete organization with existing users or events! Please reassign users and events first.'); window.location.href='organization_list.php';</script>";
    exit();
}

// Delete organization
$deleteQuery = "DELETE FROM organizations WHERE organization_id = ?";
$stmt = $conn->prepare($deleteQuery);
$stmt->bind_param("s", $organizationId);

if ($stmt->execute()) {
    echo "<script>alert('Organization deleted successfully!'); window.location.href='organization_list.php';</script>";
} else {
    echo "<script>alert('Failed to delete organization!'); window.location.href='organization_list.php';</script>";
}
$stmt->close();

mysqli_close($conn);
?>
