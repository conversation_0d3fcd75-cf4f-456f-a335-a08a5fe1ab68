<?php
require("../database.php");

echo "<h2>Debug: Profile Role IDs</h2>";

// Check all profile role IDs in the database
$query = "SELECT DISTINCT profile_role_id, COUNT(*) as count FROM profile GROUP BY profile_role_id";
$result = mysqli_query($conn, $query);

echo "<h3>All Role IDs in Database:</h3>";
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Role ID</th><th>Count</th></tr>";

while ($row = mysqli_fetch_assoc($result)) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($row['profile_role_id']) . "</td>";
    echo "<td>" . $row['count'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check specific users with their role IDs
$userQuery = "SELECT profile_id, profile_name, profile_email, profile_role_id FROM profile ORDER BY profile_name";
$userResult = mysqli_query($conn, $userQuery);

echo "<h3>All Users and Their Role IDs:</h3>";
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Profile Name</th><th>Email</th><th>Role ID</th></tr>";

while ($userRow = mysqli_fetch_assoc($userResult)) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($userRow['profile_name']) . "</td>";
    echo "<td>" . htmlspecialchars($userRow['profile_email']) . "</td>";
    echo "<td>" . htmlspecialchars($userRow['profile_role_id']) . "</td>";
    echo "</tr>";
}
echo "</table>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background-color: #f5f5f5;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #007cba;
    padding-bottom: 10px;
}

table {
    background-color: white;
    width: 100%;
}

th {
    background-color: #007cba;
    color: white;
    padding: 8px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}
</style>
