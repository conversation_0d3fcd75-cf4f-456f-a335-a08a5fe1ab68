<?php
require("../database.php");

// Migration script to change admin_id to role_id and admin_name to role_name
// in organization_admins table

echo "<h2>Database Migration: admin_id → role_id, admin_name → role_name</h2>";

try {
    // Start transaction
    $conn->begin_transaction();
    
    // Step 1: Add new columns
    echo "<p>Step 1: Adding new columns...</p>";
    
    $addRoleIdQuery = "ALTER TABLE organization_admins ADD COLUMN role_id INT AUTO_INCREMENT PRIMARY KEY FIRST";
    if ($conn->query($addRoleIdQuery)) {
        echo "✅ Added role_id column<br>";
    } else {
        throw new Exception("Failed to add role_id column: " . $conn->error);
    }
    
    $addRoleNameQuery = "ALTER TABLE organization_admins ADD COLUMN role_name VARCHAR(255) AFTER profile_id";
    if ($conn->query($addRoleNameQuery)) {
        echo "✅ Added role_name column<br>";
    } else {
        throw new Exception("Failed to add role_name column: " . $conn->error);
    }
    
    // Step 2: Copy data from admin_name to role_name
    echo "<p>Step 2: Copying data from admin_name to role_name...</p>";
    
    $copyDataQuery = "UPDATE organization_admins SET role_name = admin_name WHERE admin_name IS NOT NULL";
    if ($conn->query($copyDataQuery)) {
        echo "✅ Copied admin_name data to role_name<br>";
    } else {
        throw new Exception("Failed to copy data: " . $conn->error);
    }
    
    // Step 3: Drop old admin_id column (it will be replaced by the new role_id)
    echo "<p>Step 3: Removing old admin_id column...</p>";
    
    $dropAdminIdQuery = "ALTER TABLE organization_admins DROP COLUMN admin_id";
    if ($conn->query($dropAdminIdQuery)) {
        echo "✅ Dropped old admin_id column<br>";
    } else {
        throw new Exception("Failed to drop admin_id column: " . $conn->error);
    }
    
    // Step 4: Drop admin_name column
    echo "<p>Step 4: Removing admin_name column...</p>";
    
    $dropAdminNameQuery = "ALTER TABLE organization_admins DROP COLUMN admin_name";
    if ($conn->query($dropAdminNameQuery)) {
        echo "✅ Dropped admin_name column<br>";
    } else {
        throw new Exception("Failed to drop admin_name column: " . $conn->error);
    }
    
    // Step 5: Update profile table to reference role_id instead of admin_id
    echo "<p>Step 5: Updating profile table references...</p>";
    
    // First, check if admin_id column exists in profile table
    $checkProfileQuery = "SHOW COLUMNS FROM profile LIKE 'admin_id'";
    $result = $conn->query($checkProfileQuery);
    
    if ($result->num_rows > 0) {
        // Add role_id column to profile table
        $addProfileRoleIdQuery = "ALTER TABLE profile ADD COLUMN role_id INT AFTER admin_id";
        if ($conn->query($addProfileRoleIdQuery)) {
            echo "✅ Added role_id column to profile table<br>";
        }
        
        // Copy admin_id values to role_id in profile table
        $copyProfileDataQuery = "UPDATE profile SET role_id = admin_id WHERE admin_id IS NOT NULL";
        if ($conn->query($copyProfileDataQuery)) {
            echo "✅ Copied admin_id to role_id in profile table<br>";
        }
        
        // Drop admin_id column from profile table
        $dropProfileAdminIdQuery = "ALTER TABLE profile DROP COLUMN admin_id";
        if ($conn->query($dropProfileAdminIdQuery)) {
            echo "✅ Dropped admin_id column from profile table<br>";
        }
    } else {
        echo "ℹ️ admin_id column not found in profile table (already migrated)<br>";
    }
    
    // Step 6: Show final table structure
    echo "<p>Step 6: Final table structure:</p>";
    
    $showStructureQuery = "DESCRIBE organization_admins";
    $result = $conn->query($showStructureQuery);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Commit transaction
    $conn->commit();
    echo "<p><strong>✅ Migration completed successfully!</strong></p>";
    
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    echo "<p><strong>❌ Migration failed: " . $e->getMessage() . "</strong></p>";
    echo "<p>All changes have been rolled back.</p>";
}

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background-color: #f5f5f5;
}

h2 {
    color: #333;
    border-bottom: 2px solid #007cba;
    padding-bottom: 10px;
}

p {
    margin: 10px 0;
}

table {
    background-color: white;
    width: 100%;
}

th {
    background-color: #007cba;
    color: white;
    padding: 8px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}
</style>
