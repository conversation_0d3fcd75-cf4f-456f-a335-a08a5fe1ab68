<?php
require("../database.php");
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_name = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    $session_profile_id = $_SESSION['profile_id'];

    // Get user's organization via admin_id
    $orgQuery = "SELECT oa.organization_id FROM profile p
                 LEFT JOIN organization_admins oa ON p.admin_id = oa.admin_id
                 WHERE p.profile_id = ?";
    $stmt = $conn->prepare($orgQuery);
    $stmt->bind_param("s", $session_profile_id);
    $stmt->execute();
    $stmt->bind_result($user_organization_id);
    $stmt->fetch();
    $stmt->close();

    // Check access permissions
    $is_master_admin = ($admin_roleid == 'gPHOfKV0sL');
    $is_organization_admin = false;

    if (!$is_master_admin) {
        // Check if user is an organization admin
        $adminCheckQuery = "SELECT COUNT(*) as count FROM organization_admins WHERE profile_id = ? AND status = 1";
        $stmt = $conn->prepare($adminCheckQuery);
        $stmt->bind_param("s", $session_profile_id);
        $stmt->execute();
        $stmt->bind_result($admin_count);
        $stmt->fetch();
        $stmt->close();
        $is_organization_admin = ($admin_count > 0);
    }

    // Redirect if user doesn't have proper permissions
    if (!$is_master_admin && !$is_organization_admin) {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}
// Build queries based on user permissions
if ($is_master_admin) {
    // Master admin can see all events and roles
    $eventQuery = "SELECT DISTINCT em.event_name, em.event_mgm_id, o.organization_name
                   FROM event_mgm em
                   INNER JOIN event_role_mgm er ON em.event_mgm_id = er.event_mgm_id
                   LEFT JOIN organizations o ON em.organization_id = o.organization_id
                   ORDER BY o.organization_name, em.event_name";

    $query = "
        SELECT DISTINCT
            er.event_mgm_id,
            er.profile_id,
            em.event_name,
            p.profile_name,
            p.profile_email,
            p.profile_status,
            o.organization_name
        FROM event_role_mgm er
        INNER JOIN event_mgm em ON er.event_mgm_id = em.event_mgm_id
        INNER JOIN profile p ON er.profile_id = p.profile_id
        LEFT JOIN organizations o ON em.organization_id = o.organization_id
        ORDER BY o.organization_name, em.event_name, p.profile_name
    ";
} else {
    // Organization admin can only see events from their organization
    $eventQuery = "SELECT DISTINCT em.event_name, em.event_mgm_id, o.organization_name
                   FROM event_mgm em
                   INNER JOIN event_role_mgm er ON em.event_mgm_id = er.event_mgm_id
                   LEFT JOIN organizations o ON em.organization_id = o.organization_id
                   WHERE em.organization_id = '$user_organization_id'
                   ORDER BY em.event_name";

    $query = "
        SELECT DISTINCT
            er.event_mgm_id,
            er.profile_id,
            em.event_name,
            p.profile_name,
            p.profile_email,
            p.profile_status,
            o.organization_name
        FROM event_role_mgm er
        INNER JOIN event_mgm em ON er.event_mgm_id = em.event_mgm_id
        INNER JOIN profile p ON er.profile_id = p.profile_id
        LEFT JOIN organizations o ON em.organization_id = o.organization_id
        WHERE em.organization_id = '$user_organization_id'
        ORDER BY em.event_name, p.profile_name
    ";
}

$eventResult = mysqli_query($conn, $eventQuery);


// Execute the query
$result = mysqli_query($conn, $query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Role List</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.8.1/font/bootstrap-icons.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f3f4f6;
            font-family: 'Inter', sans-serif;
        }

        .main-title {
            font-size: 36px;
            font-weight: 800;
            margin: 30px 0;
            text-align: center;
            color: #1f2937;
        }

        .header-section {
            background-color: #ffffff;
            padding: 25px 0;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 30px;
            box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
        }

        .table-container {
            margin-top: 20px;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.05);
            overflow-x: auto;
        }

        .table th, .table td {
            padding: 16px;
            text-align: center;
            vertical-align: middle;
            font-size: 16px;
            border: none;
            cursor: pointer;
        }

        .table th {
            background-color: #f9fafb;
            font-weight: 700;
            color: #1f2937;
        }

        .table tbody tr {
            border-bottom: 1px solid #e5e7eb;
        }

        .table tbody tr:hover {
            background-color: #f3f4f6;
        }

        .table-container table td.status-active {
            color: #10b981 !important;
            font-weight: bold;
        }

        .table-container table td.status-banned {
            color: #ef4444 !important;
            font-weight: bold;
        }

        .search-box input, .role-box select {
            padding: 14px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: border-color 0.2s ease;
        }

        .search-box input:focus, .role-box select:focus {
            border-color: #3b82f6;
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        .role-box select {
            width: 300px;
        }

        footer {
            background-color: #ffffff;
            padding: 20px 0;
            margin-top: 40px;
            border-top: 1px solid #e5e7eb;
            box-shadow: 0px -1px 3px rgba(0, 0, 0, 0.1);
        }

        footer .social-icons a {
            color: #6b7280;
            transition: color 0.2s ease;
        }

        footer .social-icons a:hover {
            color: #3b82f6;
        }

        .table th.sortable:hover {
            background-color: #f3f4f6;
        }

        .table th.sortable::after {
            content: "\f0dc";
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            padding-left: 10px;
            opacity: 0.5;
        }

        .table th.sortable.sorted-asc::after {
            content: "\f0de";
        }

        .table th.sortable.sorted-desc::after {
            content: "\f0dd";
        }

        .pagination-info {
            font-size: 14px;
            color: #4b5563;
        }

        .entries-select {
            display: flex;
            align-items: center;
            width: 200px;
        }

        .entries-select label {
            margin-right: 8px;
            font-size: 14px;
            color: #4b5563;
        }

        .table-container .icon {
            font-size: 20px;
            cursor: pointer;
            margin: 0 5px;
        }

        .table-container .icon.edit-icon {
            color: #3b82f6;
            font-size:24px;
        }

        .table-container .icon.delete-icon {
            color: #ef4444;
             font-size:24px;
        }

        .table-container .icon:hover {
            opacity: 0.7;
        }

        @media (max-width: 768px) {
            /* Adjust header font size */
            .header-section h2 {
                font-size: 24px;
            }

            /* Stack search box, role dropdown, and add new button vertically */
            .d-flex {
                flex-direction: column;
                align-items: flex-start;
            }

            .search-box, .role-box, .btn-primary {
                width: 100%;
                margin-bottom: 15px; /* Add space between elements */
            }

            /* Ensure buttons are full width on mobile */
            .btn-primary {
                display: block;
                width: 100%;
                text-align: center;
            }

            /* Adjust input and select for better mobile display */
            .search-box input, .role-box select {
                width: 100%;
                font-size: 14px;
            }

            /* Adjust padding for mobile */
            .search-box input, .role-box select, .btn-primary {
                padding: 10px;
            }

            /* Reduce table padding for better fit */
            .table th, .table td {
                padding: 12px;
            }

            /* Optimize table for mobile */
            .table-container {
                overflow-x: auto;
            }

            .table th, .table td {
                white-space: nowrap;
            }

            .table-container table {
                min-width: 600px;
            }

            /* Reduce button size on mobile */
            .btn-action {
                width: 100px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body class="bg-gray-100">

    <!-- Header -->
    <?php include("admin_header.php"); ?>

    <!-- Header Section -->
    <div class="header-section">
        <h2 class="main-title">Event Role Management</h2>
    </div>

    <!-- Search Box, Role Dropdown, and Add New Button -->
    <div class="container mx-auto px-4">
        <div class="d-flex justify-content-between mb-6">
            <div class="search-box me-3" style="flex-grow: 1;">
                <input type="text" id="searchBox" class="form-control" placeholder="Search User...">
            </div>

            <a href="add_event_role.php" class="btn btn-primary">Add Event Role</a>
        </div>

        <!-- User List Table -->
        <div class="table-container">
        <table class="table">
                <thead>
                    <tr>
                        <th class="sortable">No.</th>
                        <th class="sortable">Full Name</th>
                        <th class="sortable">Email</th>
                        <th class="sortable">Event</th>
                        <?php if ($is_master_admin) { echo "<th class='sortable'>Organization</th>"; } ?>
                        <th class="sortable">Account Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="userListTable">
                    <?php
                    $counter = 1; // Counter for row numbers
                    while ($row = mysqli_fetch_assoc($result)) {
                        $statusText = $row['profile_status'] == 0 ? 'Active' : 'Banned';
                        $statusClass = $row['profile_status'] == 0 ? 'status-active' : 'status-banned';
                        echo "<tr>
                                <td>{$counter}</td>
                                <td>{$row['profile_name']}</td>
                                <td>{$row['profile_email']}</td>
                                <td>{$row['event_name']}</td>";

                        if ($is_master_admin) {
                            echo "<td>{$row['organization_name']}</td>";
                        }

                        echo "<td class='{$statusClass}'>{$statusText}</td>";?>
                           <td>
    <a href="edit_event_role.php?profile_id=<?php echo htmlspecialchars($row['profile_id'], ENT_QUOTES, 'UTF-8'); ?>" class="icon edit-icon">
        <i class="bi bi-pencil"></i>
    </a>
    <form action="delete_event_role.php" method="POST" style="display:inline;">
        <input type="hidden" name="profile_id" value="<?php echo htmlspecialchars($row['profile_id'], ENT_QUOTES, 'UTF-8'); ?>">
        <input type="hidden" name="event_mgm_id" value="<?php echo htmlspecialchars($row['event_mgm_id'], ENT_QUOTES, 'UTF-8'); ?>">
        <button type="submit" class="icon delete-icon" onclick="return confirm('Are you sure you want to delete this record?');">
            <i class="bi bi-trash"></i>
        </button>
    </form>
</td>


                            <?php
                                echo"
                            </tr>";
                        $counter++;
                    }
                    ?>
                </tbody>
            </table>
        </div>

    </div>

    <!-- Footer -->
    <?php include("admin_footer.php"); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>

    <script>


document.addEventListener('DOMContentLoaded', () => {
    const userListTable = document.getElementById('userListTable');
    const searchBox = document.getElementById('searchBox');
    let sortDirection = {};

    // Search Users
    searchBox.addEventListener('input', () => {
        const searchTerm = searchBox.value.toLowerCase();
        const rows = userListTable.querySelectorAll('tr');

        rows.forEach(row => {
            const rowText = row.textContent.toLowerCase();
            row.style.display = rowText.includes(searchTerm) ? '' : 'none';
        });
    });

    // Sorting functionality
    document.querySelectorAll('.table th').forEach(header => {
        if (header.classList.contains('sortable')) {
            header.addEventListener('click', () => sortTable(header));
        }
    });

    function sortTable(header) {
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const direction = sortDirection[columnIndex] = !sortDirection[columnIndex];

        const rows = Array.from(userListTable.querySelectorAll('tr'));
        rows.sort((a, b) => {
            const aText = a.children[columnIndex].textContent.trim();
            const bText = b.children[columnIndex].textContent.trim();
            return direction ? aText.localeCompare(bText) : bText.localeCompare(aText);
        });

        userListTable.innerHTML = '';
        rows.forEach(row => userListTable.appendChild(row));

        header.classList.remove('sorted-asc', 'sorted-desc');
        header.classList.add(direction ? 'sorted-asc' : 'sorted-desc');
    }

    // Delete User with confirmation alert
    window.deleteUser = function(icon) {
        if (confirm("Are you sure you want to delete this user?")) {
            const row = icon.closest('tr');
            row.remove();
        }
    };
});


    </script>
</body>
</html>
