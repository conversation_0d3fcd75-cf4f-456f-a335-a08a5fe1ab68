<?php
require("../database.php");
require_once __DIR__ . '/MAILER/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

// Authentication check
$adminisLoggedIn = isset($_SESSION['profile_email']);
if (!$adminisLoggedIn) {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

// Get parameters
$event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
if (empty($event_mgm_id)) {
    die("No event selected.");
}

// Get event name
$event_query = "SELECT event_name FROM event_mgm WHERE event_mgm_id = ? LIMIT 1";
$event_stmt = $conn->prepare($event_query);
$event_stmt->bind_param("s", $event_mgm_id);
$event_stmt->execute();
$event_result = $event_stmt->get_result();
$event_name = "Event";
if ($event_result->num_rows > 0) {
    $event_data = $event_result->fetch_assoc();
    $event_name = $event_data['event_name'];
}
$event_stmt->close();

// Get all categories for this event
$category_query = "
    SELECT DISTINCT ec.category_id, ec.category_name
    FROM event_mgm em
    JOIN event_category ec ON em.category_id = ec.category_id
    WHERE em.event_mgm_id = ?
    ORDER BY ec.category_name
";
$category_stmt = $conn->prepare($category_query);
$category_stmt->bind_param("s", $event_mgm_id);
$category_stmt->execute();
$category_result = $category_stmt->get_result();

$categories = [];
while ($cat_row = $category_result->fetch_assoc()) {
    $categories[] = [
        'category_id' => $cat_row['category_id'],
        'category_name' => $cat_row['category_name']
    ];
    }
$category_stmt->close();
    
if (empty($categories)) {
    die("No categories found for this event.");
}

// Get overall country statistics for the entire event
$overall_country_query = "
    SELECT 
        rf.nationality,
        COUNT(*) as participant_count
    FROM registration_form rf
    JOIN event_mgm em ON em.event_id = rf.event_id AND em.category_id = rf.category_id
    WHERE em.event_mgm_id = ?
    GROUP BY rf.nationality
    ORDER BY participant_count DESC, rf.nationality ASC
";
$overall_country_stmt = $conn->prepare($overall_country_query);
$overall_country_stmt->bind_param("s", $event_mgm_id);
$overall_country_stmt->execute();
$overall_country_result = $overall_country_stmt->get_result();

$overall_countries = [];
$total_participants = 0;
while ($country_row = $overall_country_result->fetch_assoc()) {
    $overall_countries[] = $country_row;
    $total_participants += $country_row['participant_count'];
}
$overall_country_stmt->close();

$spreadsheet = new Spreadsheet();
$spreadsheet->removeSheetByIndex(0); // Remove default sheet

// Create Summary Sheet
$summary_sheet = $spreadsheet->createSheet();
$summary_sheet->setTitle('Summary');

// Summary title
$summary_sheet->setCellValue('A1', $event_name . ' - Participants Summary');
$summary_sheet->mergeCells('A1:C1');
$summary_sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
$summary_sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

// Total participants
$summary_sheet->setCellValue('A3', 'Total Participants:');
$summary_sheet->setCellValue('B3', $total_participants);
$summary_sheet->getStyle('A3')->getFont()->setBold(true);
$summary_sheet->getStyle('B3')->getFont()->setBold(true);

// Total countries
$summary_sheet->setCellValue('A4', 'Total Countries:');
$summary_sheet->setCellValue('B4', count($overall_countries));
$summary_sheet->getStyle('A4')->getFont()->setBold(true);
$summary_sheet->getStyle('B4')->getFont()->setBold(true);

// Country breakdown header
$summary_sheet->setCellValue('A6', 'Country Breakdown');
$summary_sheet->mergeCells('A6:C6');
$summary_sheet->getStyle('A6')->getFont()->setBold(true)->setSize(14);
$summary_sheet->getStyle('A6')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

// Country breakdown headers
$summary_sheet->setCellValue('A8', 'Country');
$summary_sheet->setCellValue('B8', 'Participants');
$summary_sheet->setCellValue('C8', 'Percentage');
$summary_sheet->getStyle('A8:C8')->getFont()->setBold(true);
$summary_sheet->getStyle('A8:C8')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
$summary_sheet->getStyle('A8:C8')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFD9D9D9');

// Country breakdown data
$rowNum = 9;
foreach ($overall_countries as $country) {
    $percentage = $total_participants > 0 ? round(($country['participant_count'] / $total_participants) * 100, 1) : 0;
    $summary_sheet->setCellValue('A' . $rowNum, $country['nationality']);
    $summary_sheet->setCellValue('B' . $rowNum, $country['participant_count']);
    $summary_sheet->setCellValue('C' . $rowNum, $percentage . '%');
    $rowNum++;
}

// Auto-size summary columns
foreach (range('A', 'C') as $col) {
    $summary_sheet->getColumnDimension($col)->setAutoSize(true);
}

// Create All Players Sheet
$all_players_sheet = $spreadsheet->createSheet();
$all_players_sheet->setTitle('All Players');

// All Players title
$all_players_sheet->setCellValue('A1', $event_name . ' - All Participants by Category');
$all_players_sheet->mergeCells('A1:L1');
$all_players_sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
$all_players_sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

// Total participants in all players sheet
$all_players_sheet->setCellValue('A2', 'Total Participants: ' . $total_participants . ' | Total Categories: ' . count($categories));
$all_players_sheet->mergeCells('A2:L2');
$all_players_sheet->getStyle('A2')->getFont()->setBold(true)->setSize(12);
$all_players_sheet->getStyle('A2')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
$all_players_sheet->getStyle('A2')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFE6F3FF');

$headers = [
    'No',
    'Full Name',
    'Player Email',
    'IC Number',
    'Nationality',
    'Gender',
    'Date of Birth',
    'Contact Number',
    'Handicap',
    'Shirt Type',
    'Shirt Size',
    'Category'
];

// Header row for all players sheet
$all_players_sheet->fromArray($headers, null, 'A4');
$all_players_sheet->getStyle('A4:L4')->getFont()->setBold(true);
$all_players_sheet->getStyle('A4:L4')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
$all_players_sheet->getStyle('A4:L4')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFD9D9D9');

$all_players_row = 5;
$all_players_counter = 1;

foreach ($categories as $cat) {
    $category_id = $cat['category_id'];
    $category_name = $cat['category_name'];

    // Query participants for this category
    $participants_query = "
        SELECT 
            rf.form_id, 
            rf.fullname, 
            p.profile_email, 
            rf.ic_number,
            rf.nationality,
            rf.gender,
            rf.date_of_birth,
            rf.contant_number,
            rf.handicap,
            pei.email,
            pei.golf_shirt_type,
            pei.golf_shirt_size
        FROM registration_form rf
        JOIN profile p ON p.profile_id = rf.profile_id
        LEFT JOIN player_email_info pei ON rf.form_id = pei.form_id
        JOIN event_mgm em ON em.event_id = rf.event_id AND em.category_id = rf.category_id
        WHERE em.event_mgm_id = ? AND rf.category_id = ?
        ORDER BY rf.fullname ASC
    ";
    $stmt = $conn->prepare($participants_query);
    $stmt->bind_param("ss", $event_mgm_id, $category_id);
$stmt->execute();
$result = $stmt->get_result();

    $participants = [];
    while ($row = $result->fetch_assoc()) {
        $participants[] = $row;
    }
    $stmt->close();

    // Get country statistics for this category
    $category_country_query = "
        SELECT 
            rf.nationality,
            COUNT(*) as participant_count
        FROM registration_form rf
        JOIN event_mgm em ON em.event_id = rf.event_id AND em.category_id = rf.category_id
        WHERE em.event_mgm_id = ? AND rf.category_id = ?
        GROUP BY rf.nationality
        ORDER BY participant_count DESC, rf.nationality ASC
    ";
    $category_country_stmt = $conn->prepare($category_country_query);
    $category_country_stmt->bind_param("ss", $event_mgm_id, $category_id);
    $category_country_stmt->execute();
    $category_country_result = $category_country_stmt->get_result();

    $category_countries = [];
    $category_total = 0;
    while ($country_row = $category_country_result->fetch_assoc()) {
        $category_countries[] = $country_row;
        $category_total += $country_row['participant_count'];
    }
    $category_country_stmt->close();

    $sheet = $spreadsheet->createSheet();
    $sheet->setTitle(mb_substr($category_name, 0, 31));

    // Title row
    $sheet->setCellValue('A1', $event_name . ' - ' . $category_name . ' Participants');
    $sheet->mergeCells('A1:L1');
    $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
    $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

    // Category statistics
    $sheet->setCellValue('A2', 'Total Participants: ' . $category_total . ' | Countries: ' . count($category_countries));
    $sheet->mergeCells('A2:L2');
    $sheet->getStyle('A2')->getFont()->setBold(true)->setSize(12);
    $sheet->getStyle('A2')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('A2')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFE6F3FF');

    // Header row
    $sheet->fromArray($headers, null, 'A4');
    $sheet->getStyle('A4:L4')->getFont()->setBold(true);
    $sheet->getStyle('A4:L4')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('A4:L4')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFD9D9D9');

    // Data rows
    $rowNum = 5;
$counter = 1;

    // Add category header to all players sheet
    $all_players_sheet->setCellValue('A' . $all_players_row, $category_name);
    $all_players_sheet->mergeCells('A' . $all_players_row . ':L' . $all_players_row);
    $all_players_sheet->getStyle('A' . $all_players_row)->getFont()->setBold(true)->setSize(12);
    $all_players_sheet->getStyle('A' . $all_players_row)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFE6F3FF');
    $all_players_row++;

    foreach ($participants as $row) {
        // Handle date format "08/01/2002" (DD/MM/YYYY)
        $date_of_birth = '';
        if (!empty($row['date_of_birth'])) {
            // Check if the date is already in DD/MM/YYYY format
            if (preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $row['date_of_birth'])) {
                $date_of_birth = $row['date_of_birth'];
            } else {
                // Try to parse other date formats
                $timestamp = strtotime($row['date_of_birth']);
                if ($timestamp !== false) {
                    $date_of_birth = date('d/m/Y', $timestamp);
                } else {
                    $date_of_birth = $row['date_of_birth']; // Keep original if parsing fails
                }
            }
        }
        
        $rowData = [
        $counter,
        $row['fullname'],
        $row['email'],
        $row['ic_number'],
        $row['nationality'],
        $row['gender'],
        $date_of_birth,
        $row['contant_number'],
            $row['handicap'],
        $row['golf_shirt_type'],
        $row['golf_shirt_size'],
            $category_name
        ];
        $sheet->fromArray($rowData, null, 'A' . $rowNum);

        // Add to all players sheet
        $all_players_rowData = [
            $all_players_counter,
            $row['fullname'],
            $row['email'],
            $row['ic_number'],
            $row['nationality'],
            $row['gender'],
            $date_of_birth,
            $row['contant_number'],
            $row['handicap'],
            $row['golf_shirt_type'],
            $row['golf_shirt_size'],
            $category_name
        ];
        $all_players_sheet->fromArray($all_players_rowData, null, 'A' . $all_players_row);
        $all_players_row++;
        $all_players_counter++;

        $rowNum++;
    $counter++;
}

    // Add country breakdown for this category
    $startRow = $rowNum + 2;
    $sheet->setCellValue('A' . $startRow, 'Country Breakdown for ' . $category_name);
    $sheet->mergeCells('A' . $startRow . ':C' . $startRow);
    $sheet->getStyle('A' . $startRow)->getFont()->setBold(true)->setSize(12);
    $sheet->getStyle('A' . $startRow)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

    $startRow += 2;
    $sheet->setCellValue('A' . $startRow, 'Country');
    $sheet->setCellValue('B' . $startRow, 'Participants');
    $sheet->setCellValue('C' . $startRow, 'Percentage');
    $sheet->getStyle('A' . $startRow . ':C' . $startRow)->getFont()->setBold(true);
    $sheet->getStyle('A' . $startRow . ':C' . $startRow)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('A' . $startRow . ':C' . $startRow)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFE6E6E6');

    $startRow++;
    foreach ($category_countries as $country) {
        $percentage = $category_total > 0 ? round(($country['participant_count'] / $category_total) * 100, 1) : 0;
        $sheet->setCellValue('A' . $startRow, $country['nationality']);
        $sheet->setCellValue('B' . $startRow, $country['participant_count']);
        $sheet->setCellValue('C' . $startRow, $percentage . '%');
        $startRow++;
    }

    // Auto-size columns
    foreach (range('A', 'L') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }

    // Add blank row after category in all players sheet
    $all_players_row++;
}

// Finalize all players sheet
// Auto-size columns for all players sheet
foreach (range('A', 'L') as $col) {
    $all_players_sheet->getColumnDimension($col)->setAutoSize(true);
}

// Apply borders to all players data
if ($all_players_row > 5) {
    $all_players_sheet->getStyle('A4:L' . ($all_players_row - 2))->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
}

// Remove the first (empty) sheet if it still exists
if ($spreadsheet->getSheetCount() > count($categories) + 2) {
    $spreadsheet->removeSheetByIndex(0);
}

$event_name_clean = preg_replace('/[^A-Za-z0-9\-]/', '_', $event_name);
$filename = "{$event_name_clean}-Participants-" . date('Ymd_His') . ".xlsx";

header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header("Content-Disposition: attachment;filename=\"{$filename}\"");
header('Cache-Control: max-age=0');
header('Pragma: public');

$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit();
?> 