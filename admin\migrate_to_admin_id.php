<?php
require("../database.php");

// Check if user is logged in and is master admin
$adminisLoggedIn = isset($_SESSION['profile_email']);
if ($adminisLoggedIn) {
    $admin_roleid = $_SESSION['profile_role_id'];
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit();
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Migrate to Admin ID System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .container { max-width: 800px; margin: 50px auto; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Migrate Profile Table to Use admin_id</h1>
        <p>This script will:</p>
        <ul>
            <li>1. Add admin_id column to profile table</li>
            <li>2. Migrate existing organization_id data to organization_admins table</li>
            <li>3. Update profile table to use admin_id references</li>
            <li>4. Remove organization_id column from profile table</li>
        </ul>
        <hr>

<?php

$success_count = 0;
$error_count = 0;

// Step 1: Add admin_id column to profile table
echo "<div class='step'>";
echo "<h3>Step 1: Adding admin_id column to profile table</h3>";
$check_column = "SHOW COLUMNS FROM profile LIKE 'admin_id'";
$result = mysqli_query($conn, $check_column);
if (mysqli_num_rows($result) == 0) {
    $sql = "ALTER TABLE `profile` ADD COLUMN `admin_id` int(11) NULL AFTER `profile_role_id`";
    if (mysqli_query($conn, $sql)) {
        echo "<p class='success'>✓ Added admin_id column to profile table</p>";
        $success_count++;
    } else {
        echo "<p class='error'>✗ Error adding admin_id to profile: " . mysqli_error($conn) . "</p>";
        $error_count++;
    }
} else {
    echo "<p class='info'>ℹ admin_id column already exists in profile table</p>";
}
echo "</div>";

// Step 2: Migrate existing organization_id data to organization_admins table
echo "<div class='step'>";
echo "<h3>Step 2: Migrating existing organization assignments</h3>";

// Get users with organization_id assignments
$check_org_column = "SHOW COLUMNS FROM profile LIKE 'organization_id'";
$result = mysqli_query($conn, $check_org_column);
if (mysqli_num_rows($result) > 0) {
    $users_query = "SELECT profile_id, organization_id FROM profile WHERE organization_id IS NOT NULL AND organization_id != ''";
    $users_result = mysqli_query($conn, $users_query);
    
    if ($users_result && mysqli_num_rows($users_result) > 0) {
        while ($user = mysqli_fetch_assoc($users_result)) {
            $profile_id = $user['profile_id'];
            $organization_id = $user['organization_id'];
            
            // Check if assignment already exists in organization_admins
            $check_query = "SELECT admin_id FROM organization_admins WHERE profile_id = ? AND organization_id = ?";
            $stmt = $conn->prepare($check_query);
            $stmt->bind_param("ss", $profile_id, $organization_id);
            $stmt->execute();
            $check_result = $stmt->get_result();
            
            if ($check_result->num_rows == 0) {
                // Insert into organization_admins
                $insert_query = "INSERT INTO organization_admins (organization_id, profile_id) VALUES (?, ?)";
                $stmt2 = $conn->prepare($insert_query);
                $stmt2->bind_param("ss", $organization_id, $profile_id);
                
                if ($stmt2->execute()) {
                    $admin_id = $conn->insert_id;
                    
                    // Update profile table with admin_id
                    $update_query = "UPDATE profile SET admin_id = ? WHERE profile_id = ?";
                    $stmt3 = $conn->prepare($update_query);
                    $stmt3->bind_param("is", $admin_id, $profile_id);
                    
                    if ($stmt3->execute()) {
                        echo "<p class='success'>✓ Migrated user {$profile_id} to organization {$organization_id} (admin_id: {$admin_id})</p>";
                        $success_count++;
                    } else {
                        echo "<p class='error'>✗ Error updating profile with admin_id for {$profile_id}</p>";
                        $error_count++;
                    }
                    $stmt3->close();
                } else {
                    echo "<p class='error'>✗ Error creating organization_admin record for {$profile_id}</p>";
                    $error_count++;
                }
                $stmt2->close();
            } else {
                // Get existing admin_id and update profile
                $existing = $check_result->fetch_assoc();
                $admin_id = $existing['admin_id'];
                
                $update_query = "UPDATE profile SET admin_id = ? WHERE profile_id = ?";
                $stmt3 = $conn->prepare($update_query);
                $stmt3->bind_param("is", $admin_id, $profile_id);
                
                if ($stmt3->execute()) {
                    echo "<p class='info'>ℹ User {$profile_id} already has organization assignment (admin_id: {$admin_id})</p>";
                } else {
                    echo "<p class='error'>✗ Error updating existing profile with admin_id for {$profile_id}</p>";
                    $error_count++;
                }
                $stmt3->close();
            }
            $stmt->close();
        }
    } else {
        echo "<p class='info'>ℹ No users with organization assignments found</p>";
    }
} else {
    echo "<p class='info'>ℹ organization_id column does not exist in profile table</p>";
}
echo "</div>";

// Step 3: Add foreign key constraint
echo "<div class='step'>";
echo "<h3>Step 3: Adding foreign key constraint</h3>";
$sql = "ALTER TABLE `profile` ADD CONSTRAINT `fk_profile_admin` FOREIGN KEY (`admin_id`) REFERENCES `organization_admins`(`admin_id`) ON UPDATE CASCADE ON DELETE SET NULL";
if (mysqli_query($conn, $sql)) {
    echo "<p class='success'>✓ Added foreign key constraint for admin_id</p>";
    $success_count++;
} else {
    echo "<p class='error'>✗ Error adding foreign key constraint: " . mysqli_error($conn) . "</p>";
    $error_count++;
}
echo "</div>";

// Step 4: Remove organization_id column (optional - commented out for safety)
echo "<div class='step'>";
echo "<h3>Step 4: Remove organization_id column (OPTIONAL)</h3>";
echo "<p class='info'>⚠️ This step is commented out for safety. Uncomment if you want to remove the old column.</p>";
/*
$check_org_column = "SHOW COLUMNS FROM profile LIKE 'organization_id'";
$result = mysqli_query($conn, $check_org_column);
if (mysqli_num_rows($result) > 0) {
    // First remove foreign key constraint
    $sql = "ALTER TABLE `profile` DROP FOREIGN KEY `fk_profile_organization`";
    mysqli_query($conn, $sql);
    
    // Then remove the column
    $sql = "ALTER TABLE `profile` DROP COLUMN `organization_id`";
    if (mysqli_query($conn, $sql)) {
        echo "<p class='success'>✓ Removed organization_id column from profile table</p>";
        $success_count++;
    } else {
        echo "<p class='error'>✗ Error removing organization_id column: " . mysqli_error($conn) . "</p>";
        $error_count++;
    }
} else {
    echo "<p class='info'>ℹ organization_id column already removed</p>";
}
*/
echo "</div>";

// Summary
echo "<div class='step'>";
echo "<h3>Migration Summary</h3>";
echo "<p class='success'>✓ Successful operations: {$success_count}</p>";
if ($error_count > 0) {
    echo "<p class='error'>✗ Failed operations: {$error_count}</p>";
} else {
    echo "<p class='success'>✓ All operations completed successfully!</p>";
}

echo "<h4>Next Steps:</h4>";
echo "<ul>";
echo "<li>1. Update PHP files to use admin_id instead of organization_id</li>";
echo "<li>2. Test the organization admin assignment system</li>";
echo "<li>3. If everything works, uncomment Step 4 to remove organization_id column</li>";
echo "</ul>";
echo "</div>";

mysqli_close($conn);
?>

    </div>
</body>
</html>
